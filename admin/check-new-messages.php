<?php
/**
 * Champions Sports Bar & Grill - Check for New Messages AJAX Handler
 */

require_once 'includes/auth.php';
require_once 'config/database.php';

header('Content-Type: application/json');

$auth = new Auth();
$auth->requireLogin();

$db = getDB();

// Get the timestamp of the last check from session
$lastCheck = $_SESSION['last_message_check'] ?? date('Y-m-d H:i:s', strtotime('-1 hour'));

// Check for new messages since last check
$newMessages = $db->fetch("
    SELECT COUNT(*) as count 
    FROM contact_messages 
    WHERE created_at > :last_check AND status = 'new'
", ['last_check' => $lastCheck]);

// Update last check timestamp
$_SESSION['last_message_check'] = date('Y-m-d H:i:s');

// Return JSON response
echo json_encode([
    'hasNewMessages' => $newMessages['count'] > 0,
    'newMessageCount' => $newMessages['count'],
    'lastCheck' => $lastCheck
]);
?>
