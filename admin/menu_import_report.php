<?php
/**
 * Menu Import Report
 * Champions Sports Bar & Grill
 */

require_once 'config/database.php';

// Check if running from command line
if (php_sapi_name() !== 'cli') {
    die("This script must be run from the command line.\n");
}

function generateReport() {
    try {
        $db = getDB();
        
        echo "=== MENU IMPORT REPORT ===\n";
        echo "Generated: " . date('Y-m-d H:i:s') . "\n\n";
        
        // Get total counts
        $totalItems = $db->fetch("SELECT COUNT(*) as count FROM menu_items");
        $totalCategories = $db->fetch("SELECT COUNT(*) as count FROM menu_categories");
        
        echo "SUMMARY:\n";
        echo "- Total Menu Items: " . $totalItems['count'] . "\n";
        echo "- Total Categories: " . $totalCategories['count'] . "\n\n";
        
        // Get items by category
        $categories = $db->fetchAll("
            SELECT mc.name as category_name, COUNT(mi.id) as item_count,
                   MIN(mi.price) as min_price, MAX(mi.price) as max_price,
                   AVG(mi.price) as avg_price
            FROM menu_categories mc
            LEFT JOIN menu_items mi ON mc.id = mi.category_id
            GROUP BY mc.id, mc.name
            ORDER BY mc.sort_order, mc.name
        ");
        
        echo "CATEGORIES:\n";
        foreach ($categories as $category) {
            echo sprintf(
                "- %-20s: %2d items | Price range: $%.2f - $%.2f | Avg: $%.2f\n",
                $category['category_name'],
                $category['item_count'],
                $category['min_price'] ?? 0,
                $category['max_price'] ?? 0,
                $category['avg_price'] ?? 0
            );
        }
        
        echo "\nDETAILED LISTING:\n";
        echo str_repeat("=", 80) . "\n";
        
        foreach ($categories as $category) {
            if ($category['item_count'] == 0) continue;
            
            echo "\n" . strtoupper($category['category_name']) . ":\n";
            echo str_repeat("-", 40) . "\n";
            
            $items = $db->fetchAll("
                SELECT name, price, description
                FROM menu_items 
                WHERE category_id = (SELECT id FROM menu_categories WHERE name = :category_name)
                ORDER BY price DESC, name
            ", ['category_name' => $category['category_name']]);
            
            foreach ($items as $item) {
                $description = $item['description'] ? 
                    (strlen($item['description']) > 60 ? 
                        substr($item['description'], 0, 60) . "..." : 
                        $item['description']) : 
                    'No description';
                
                echo sprintf(
                    "%-35s $%6.2f\n    %s\n\n",
                    $item['name'],
                    $item['price'],
                    $description
                );
            }
        }
        
        // Price analysis
        echo "\nPRICE ANALYSIS:\n";
        echo str_repeat("=", 40) . "\n";
        
        $priceRanges = [
            'Under $5' => [0, 4.99],
            '$5 - $9.99' => [5, 9.99],
            '$10 - $14.99' => [10, 14.99],
            '$15 - $19.99' => [15, 19.99],
            '$20+' => [20, 999]
        ];
        
        foreach ($priceRanges as $range => $prices) {
            $count = $db->fetch("
                SELECT COUNT(*) as count 
                FROM menu_items 
                WHERE price >= :min_price AND price <= :max_price
            ", ['min_price' => $prices[0], 'max_price' => $prices[1]]);
            
            echo sprintf("%-15s: %2d items\n", $range, $count['count']);
        }
        
        // Most expensive items
        echo "\nTOP 10 MOST EXPENSIVE ITEMS:\n";
        echo str_repeat("=", 50) . "\n";
        
        $expensive = $db->fetchAll("
            SELECT mi.name, mi.price, mc.name as category
            FROM menu_items mi
            JOIN menu_categories mc ON mi.category_id = mc.id
            ORDER BY mi.price DESC
            LIMIT 10
        ");
        
        foreach ($expensive as $item) {
            echo sprintf(
                "%-30s $%6.2f (%s)\n",
                $item['name'],
                $item['price'],
                $item['category']
            );
        }
        
        echo "\n=== END REPORT ===\n";
        
    } catch (Exception $e) {
        echo "Error generating report: " . $e->getMessage() . "\n";
        exit(1);
    }
}

// Main execution
generateReport();
?>
