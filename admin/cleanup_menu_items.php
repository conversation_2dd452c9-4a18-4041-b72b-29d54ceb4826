<?php
/**
 * Cleanup Menu Items - Fix OCR errors and categorization
 * Champions Sports Bar & Grill
 */

require_once 'config/database.php';

// Check if running from command line
if (php_sapi_name() !== 'cli') {
    die("This script must be run from the command line.\n");
}

function cleanupMenuItems() {
    try {
        $db = getDB();
        
        echo "Starting menu items cleanup...\n";
        
        // Fix item names with OCR errors
        $nameFixes = [
            'Chicken Ques Adilla' => 'Chicken Quesadilla',
            'Cheese Stick S' => 'Cheese Sticks',
            'Fiest A Jalapeno Poppers' => 'Fiesta Jalapeno Poppers',
            'Chicken Mini T Acos' => 'Chicken Mini Tacos',
            'Chips & Chili C On Queso' => 'Chips & Chili Con Queso',
            'Lucky 7' => 'Lucky 7 Wings',
            'Double Do Wn (14 )' => 'Double Down (14 Wings)',
            'Thir Ty P Ack' => 'Thirty Pack Wings',
            'Champions Signa Ture Burger' => 'Champions Signature Burger',
            'Meg A Mea T Lover Burger' => 'Mega Meat Lover Burger',
            'Black -N- Bleu Burger' => 'Black-N-Bleu Burger',
            'The Bey Ond Burger' => 'The Beyond Burger',
            'Patty Mel T' => 'Patty Melt',
            'Champions Chicken S Andwich' => 'Champions Chicken Sandwich',
            'Cowbo Y Chicken S And' => 'Cowboy Chicken Sandwich',
            'Hawaiian Chicken S Andwich' => 'Hawaiian Chicken Sandwich',
            'Beef Brisket S Andwich' => 'Beef Brisket Sandwich',
            'Phill Y Steak & Cheese' => 'Philly Steak & Cheese',
            'Classic Cl Ub S Andwich' => 'Classic Club Sandwich',
            'Crisp Y Chicken S And' => 'Crispy Chicken Sandwich',
            'Turkey Mel T Del Uxe' => 'Turkey Melt Deluxe',
            'Fish S Andwich' => 'Fish Sandwich',
            'Coney Ho T Dog Meal' => 'Coney Hot Dog Meal',
            'Chicken Caes Ar Wrap' => 'Chicken Caesar Wrap',
            'Bbq Chicken B Acon Wrap' => 'BBQ Chicken Bacon Wrap',
            'Crisp Y Chicken Wrap' => 'Crispy Chicken Wrap',
            'Michig An S Alad' => 'Michigan Salad',
            'Deluxe Chicken S Alad' => 'Deluxe Chicken Salad',
            'Grilled Chicken Caes Ar S Alad' => 'Grilled Chicken Caesar Salad',
            'Chef\'S S Alad Supreme' => 'Chef\'s Salad Supreme',
            'Baja Chicken S Alad' => 'Baja Chicken Salad',
            'Black -N-Bleu Steak S Alad' => 'Black-N-Bleu Steak Salad',
            'New Y Ork Strip Steak Dinner' => 'New York Strip Steak Dinner',
            'Grilled S Almon Dinner' => 'Grilled Salmon Dinner',
            'Shrimp Skewer Pla Tter' => 'Shrimp Skewer Platter',
            'Butterfl Y Fried Shrimp Dinner' => 'Butterfly Fried Shrimp Dinner',
            'Chicken Stir Fr Y' => 'Chicken Stir Fry',
            'Homestyle Ro Ast Beef Dinner' => 'Homestyle Roast Beef Dinner',
            'Side B Aked Po Tato' => 'Side Baked Potato',
            'Side House S' => 'Side House Salad',
            'Bowl' => 'Bowl - Soup of the Day',
            'Roo Tbeer Fl' => 'Root Beer Float',
            'Deluxe Choc Ola Te Cake' => 'Deluxe Chocolate Cake',
            'Sweet Carro T Cake' => 'Sweet Carrot Cake',
            'Mount Ain High Sundae' => 'Mountain High Sundae',
            'Cinnamon- Sug Ar Pretzel Bites' => 'Cinnamon-Sugar Pretzel Bites',
            'Deluxe Fla Vored Cheesecake' => 'Deluxe Flavored Cheesecake',
            'Cinnamon Sug Ar Chips Pla Tter With T Oppings' => 'Cinnamon Sugar Chips Platter With Toppings',
            'Soft Drink S' => 'Soft Drinks',
            'Lemonade Or Iced' => 'Lemonade or Iced Tea',
            'Fruit Juice, Milk, Or Choc Ola Te Milk' => 'Fruit Juice, Milk, or Chocolate Milk',
            'Roo Tbeer On T Ap' => 'Root Beer On Tap'
        ];
        
        $fixed = 0;
        foreach ($nameFixes as $oldName => $newName) {
            $result = $db->query(
                "UPDATE menu_items SET name = :new_name WHERE name = :old_name",
                ['new_name' => $newName, 'old_name' => $oldName]
            );
            if ($result->rowCount() > 0) {
                echo "Fixed name: '$oldName' -> '$newName'\n";
                $fixed++;
            }
        }
        
        echo "Fixed $fixed item names\n";
        
        // Fix categorization - move dinner items to Entrees category
        $entreeCategoryId = getCategoryId($db, 'Entrees');
        
        $dinnerItems = [
            'New York Strip Steak Dinner',
            'Grilled Salmon Dinner',
            'Hawaiian Chicken Dinner',
            'Beef Brisket Dinner',
            'Perch Dinner',
            'Shrimp Skewer Platter',
            'Butterfly Fried Shrimp Dinner',
            'Cod Dinner',
            'Chicken Stir Fry',
            'Homestyle Roast Beef Dinner',
            'Chopped Steak Dinner'
        ];
        
        $moved = 0;
        foreach ($dinnerItems as $itemName) {
            $result = $db->query(
                "UPDATE menu_items SET category_id = :category_id WHERE name = :name",
                ['category_id' => $entreeCategoryId, 'name' => $itemName]
            );
            if ($result->rowCount() > 0) {
                echo "Moved to Entrees: $itemName\n";
                $moved++;
            }
        }
        
        // Fix beverage categorization
        $beverageCategoryId = getCategoryId($db, 'Beverages');
        
        $beverageItems = [
            'Soft Drinks',
            'Lemonade or Iced Tea',
            'Fruit Juice, Milk, or Chocolate Milk',
            'Root Beer On Tap'
        ];
        
        foreach ($beverageItems as $itemName) {
            $result = $db->query(
                "UPDATE menu_items SET category_id = :category_id WHERE name = :name",
                ['category_id' => $beverageCategoryId, 'name' => $itemName]
            );
            if ($result->rowCount() > 0) {
                echo "Moved to Beverages: $itemName\n";
                $moved++;
            }
        }
        
        echo "Moved $moved items to correct categories\n";
        
        // Clean up descriptions
        $items = $db->fetchAll("SELECT id, description FROM menu_items WHERE description IS NOT NULL AND description != ''");
        
        $cleaned = 0;
        foreach ($items as $item) {
            $cleanDescription = cleanDescription($item['description']);
            if ($cleanDescription !== $item['description']) {
                $db->query(
                    "UPDATE menu_items SET description = :description WHERE id = :id",
                    ['description' => $cleanDescription, 'id' => $item['id']]
                );
                $cleaned++;
            }
        }
        
        echo "Cleaned $cleaned item descriptions\n";
        echo "Cleanup completed successfully!\n";
        
    } catch (Exception $e) {
        echo "Error during cleanup: " . $e->getMessage() . "\n";
        exit(1);
    }
}

function getCategoryId($db, $categoryName) {
    $category = $db->fetch("SELECT id FROM menu_categories WHERE name = :name", ['name' => $categoryName]);
    
    if ($category) {
        return $category['id'];
    }
    
    // Create new category if it doesn't exist
    $sortOrder = $db->fetch("SELECT MAX(sort_order) as max_order FROM menu_categories");
    $newSortOrder = ($sortOrder['max_order'] ?? 0) + 1;
    
    $db->query(
        "INSERT INTO menu_categories (name, description, sort_order, is_active) VALUES (:name, :description, :sort_order, 1)",
        [
            'name' => $categoryName,
            'description' => "Menu items from $categoryName category",
            'sort_order' => $newSortOrder
        ]
    );
    
    return $db->getConnection()->lastInsertId();
}

function cleanDescription($description) {
    // Fix common OCR errors in descriptions
    $description = str_replace(['t oma to', 't oma toes'], ['tomato', 'tomatoes'], $description);
    $description = str_replace(['bee f', 'briske t'], ['beef', 'brisket'], $description);
    $description = str_replace(['le ttuce'], ['lettuce'], $description);
    $description = str_replace(['po tato'], ['potato'], $description);
    $description = str_replace(['gr ound'], ['ground'], $description);
    $description = str_replace(['mushr oom', 'mushr ooms'], ['mushroom', 'mushrooms'], $description);
    $description = str_replace(['f or'], ['for'], $description);
    $description = str_replace(['y our'], ['your'], $description);
    $description = str_replace(['o f'], ['of'], $description);
    $description = str_replace(['t o'], ['to'], $description);
    $description = str_replace(['an y'], ['any'], $description);
    $description = str_replace(['t opped'], ['topped'], $description);
    $description = str_replace(['t ortilla'], ['tortilla'], $description);
    $description = str_replace(['c omes'], ['comes'], $description);
    $description = str_replace(['c orn'], ['corn'], $description);
    $description = str_replace(['c olesla w'], ['coleslaw'], $description);
    $description = str_replace(['teriy aki'], ['teriyaki'], $description);
    $description = str_replace(['swee t'], ['sweet'], $description);
    $description = str_replace(['bac on'], ['bacon'], $description);
    $description = str_replace(['turke y'], ['turkey'], $description);
    $description = str_replace(['r oast'], ['roast'], $description);
    $description = str_replace(['r anch'], ['ranch'], $description);
    $description = str_replace(['smo thered'], ['smothered'], $description);
    $description = str_replace(['sautéed'], ['sauteed'], $description);
    $description = str_replace(['marina ted'], ['marinated'], $description);
    $description = str_replace(['vege table'], ['vegetable'], $description);
    $description = str_replace(['a vailable'], ['available'], $description);
    $description = str_replace(['fla vor', 'fla vored'], ['flavor', 'flavored'], $description);
    
    // Remove extra spaces
    $description = preg_replace('/\s+/', ' ', $description);
    $description = trim($description);
    
    return $description;
}

// Main execution
cleanupMenuItems();
?>
