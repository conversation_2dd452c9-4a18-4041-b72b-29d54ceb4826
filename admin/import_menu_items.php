<?php
/**
 * Import Menu Items from JSON to Database
 * Champions Sports Bar & Grill
 */

require_once 'config/database.php';

// Check if running from command line
if (php_sapi_name() !== 'cli') {
    die("This script must be run from the command line.\n");
}

function cleanText($text) {
    // Clean up OCR artifacts and special characters
    $text = str_replace(['t oma to', 't oma toes'], ['tomato', 'tomatoes'], $text);
    $text = str_replace(['bee f', 'briske t'], ['beef', 'brisket'], $text);
    $text = str_replace(['le ttuce'], ['lettuce'], $text);
    $text = str_replace(['po tato'], ['potato'], $text);
    $text = str_replace(['gr ound'], ['ground'], $text);
    $text = str_replace(['mushr oom', 'mushr ooms'], ['mushroom', 'mushrooms'], $text);
    $text = str_replace(['f or'], ['for'], $text);
    $text = str_replace(['y our'], ['your'], $text);
    $text = str_replace(['o f'], ['of'], $text);
    $text = str_replace(['t o'], ['to'], $text);
    $text = str_replace(['an y'], ['any'], $text);
    $text = str_replace(['t opped'], ['topped'], $text);
    $text = str_replace(['t ortilla'], ['tortilla'], $text);
    $text = str_replace(['c omes'], ['comes'], $text);
    $text = str_replace(['c orn'], ['corn'], $text);
    $text = str_replace(['c olesla w'], ['coleslaw'], $text);
    $text = str_replace(['c od'], ['cod'], $text);
    $text = str_replace(['c aramel'], ['caramel'], $text);
    $text = str_replace(['choc olate'], ['chocolate'], $text);
    $text = str_replace(['or ange', 'or anges'], ['orange', 'oranges'], $text);
    $text = str_replace(['teriy aki'], ['teriyaki'], $text);
    $text = str_replace(['swee t'], ['sweet'], $text);
    $text = str_replace(['bac on'], ['bacon'], $text);
    $text = str_replace(['turke y'], ['turkey'], $text);
    $text = str_replace(['r oast'], ['roast'], $text);
    $text = str_replace(['r anch'], ['ranch'], $text);
    $text = str_replace(['r aspberry'], ['raspberry'], $text);
    $text = str_replace(['smo thered'], ['smothered'], $text);
    $text = str_replace(['sautéed'], ['sauteed'], $text);
    $text = str_replace(['marina ted'], ['marinated'], $text);
    $text = str_replace(['vege table'], ['vegetable'], $text);
    $text = str_replace(['a vailable'], ['available'], $text);
    $text = str_replace(['fla vor', 'fla vored'], ['flavor', 'flavored'], $text);
    $text = str_replace(['grea t'], ['great'], $text);
    $text = str_replace(['hea t'], ['heat'], $text);
    $text = str_replace(['moun tain'], ['mountain'], $text);
    $text = str_replace(['str aw', 'str aws'], ['straw', 'straws'], $text);
    $text = str_replace(['Ques Adilla'], ['Quesadilla'], $text);
    $text = str_replace(['Stick S'], ['Sticks'], $text);
    $text = str_replace(['Fiest A'], ['Fiesta'], $text);
    
    // Remove extra spaces
    $text = preg_replace('/\s+/', ' ', $text);
    $text = trim($text);
    
    return $text;
}

function getCategoryId($db, $categoryName) {
    // Check if category exists
    $category = $db->fetch("SELECT id FROM menu_categories WHERE name = :name", ['name' => $categoryName]);
    
    if ($category) {
        return $category['id'];
    }
    
    // Create new category
    $sortOrder = $db->fetch("SELECT MAX(sort_order) as max_order FROM menu_categories");
    $newSortOrder = ($sortOrder['max_order'] ?? 0) + 1;
    
    $db->query(
        "INSERT INTO menu_categories (name, description, sort_order, is_active) VALUES (:name, :description, :sort_order, 1)",
        [
            'name' => $categoryName,
            'description' => "Menu items from $categoryName category",
            'sort_order' => $newSortOrder
        ]
    );
    
    return $db->getConnection()->lastInsertId();
}

function importMenuItems($jsonFile) {
    try {
        $db = getDB();
        
        // Read JSON file
        if (!file_exists($jsonFile)) {
            throw new Exception("JSON file not found: $jsonFile");
        }
        
        $jsonData = file_get_contents($jsonFile);
        $menuItems = json_decode($jsonData, true);
        
        if (!$menuItems) {
            throw new Exception("Failed to parse JSON file or file is empty");
        }
        
        echo "Found " . count($menuItems) . " menu items to import\n";
        
        $imported = 0;
        $skipped = 0;
        $errors = 0;
        
        foreach ($menuItems as $item) {
            try {
                // Clean up the data
                $name = cleanText($item['name']);
                $description = cleanText($item['description'] ?? '');
                $price = floatval($item['price']);
                $categoryName = $item['category'] ?? 'Uncategorized';
                
                // Skip items with invalid data
                if (empty($name) || $price <= 0) {
                    echo "Skipping invalid item: " . ($name ?: 'unnamed') . "\n";
                    $skipped++;
                    continue;
                }
                
                // Check if item already exists
                $existing = $db->fetch(
                    "SELECT id FROM menu_items WHERE name = :name",
                    ['name' => $name]
                );
                
                if ($existing) {
                    echo "Skipping existing item: $name\n";
                    $skipped++;
                    continue;
                }
                
                // Get or create category
                $categoryId = getCategoryId($db, $categoryName);
                
                // Insert menu item
                $db->query(
                    "INSERT INTO menu_items (category_id, name, description, price, is_available, is_featured, created_at, updated_at)
                     VALUES (:category_id, :name, :description, :price, 1, 0, NOW(), NOW())",
                    [
                        'category_id' => $categoryId,
                        'name' => $name,
                        'description' => $description,
                        'price' => $price
                    ]
                );
                
                echo "Imported: $name (\$$price) - $categoryName\n";
                $imported++;
                
            } catch (Exception $e) {
                echo "Error importing item: " . ($item['name'] ?? 'unknown') . " - " . $e->getMessage() . "\n";
                $errors++;
            }
        }
        
        echo "\nImport completed:\n";
        echo "- Imported: $imported items\n";
        echo "- Skipped: $skipped items\n";
        echo "- Errors: $errors items\n";
        
    } catch (Exception $e) {
        echo "Error: " . $e->getMessage() . "\n";
        exit(1);
    }
}

// Main execution
if ($argc < 2) {
    echo "Usage: php import_menu_items.php <json_file>\n";
    echo "Example: php import_menu_items.php menu_items.json\n";
    exit(1);
}

$jsonFile = $argv[1];
importMenuItems($jsonFile);
?>
