#!/usr/bin/env python3
"""
PDF Menu Processor for Champions Sports Bar
Extracts menu items from PDF and adds them to the database
"""

import sys
import os
import re
import json
from pathlib import Path

# Add the parent directory to the path to import config
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from pypdf import Pdf<PERSON>eader
except ImportError:
    print("Error: pypdf library not found. Please install it with: pip install pypdf")
    sys.exit(1)

def extract_text_from_pdf(pdf_path):
    """Extract text from PDF file"""
    try:
        reader = PdfReader(pdf_path)
        text = ""
        
        for page in reader.pages:
            text += page.extract_text() + "\n"
        
        return text
    except Exception as e:
        print(f"Error reading PDF: {e}")
        return None

def parse_menu_items(text):
    """Parse menu items from extracted text"""
    menu_items = []

    lines = text.split('\n')
    current_category = None

    # Define category mappings
    category_map = {
        'STARTERS': 'Appetizers',
        'WINGS * WINGS  * WINGS': 'Wings',
        'BURGERS & HANDHELDS': 'Burgers & Sandwiches',
        'SALADS': 'Salads',
        'ENTRÉE FAVORITES': 'Entrees',
        'SIDES & EXTRAS': 'Sides',
        'KIDS': 'Kids Menu',
        'DESSERTS': 'Desserts',
        'BEVERAGES': 'Beverages'
    }

    for i, line in enumerate(lines):
        line = line.strip()
        if not line:
            continue

        # Check if this line is a category header
        for category_key, category_name in category_map.items():
            if category_key in line:
                current_category = category_name
                break

        # Pattern for menu items: Price followed by item name and description
        # Example: "14.99 BRISKET NACHOS  — A twist on our classic nacho supreme..."
        price_item_pattern = r'^(\d+\.?\d*)\s+([A-Z][A-Z\s&\'\-\(\)]+?)\s*—\s*(.+?)$'
        match = re.match(price_item_pattern, line)

        if match:
            price = float(match.group(1))
            item_name = match.group(2).strip()
            description = match.group(3).strip()

            # Clean up item name
            item_name = re.sub(r'\s+', ' ', item_name)
            item_name = item_name.title()

            # Clean up description - remove extra characters and truncate if too long
            description = re.sub(r'[ÀÁÂÃÄÅàáâãäåÇçÈÉÊËèéêëÌÍÎÏìíîïÑñÒÓÔÕÖØòóôõöøÙÚÛÜùúûüÝýÿŸ]', '', description)
            description = re.sub(r'\s+', ' ', description)
            if len(description) > 200:
                description = description[:200] + "..."

            menu_items.append({
                'name': item_name,
                'price': price,
                'category': current_category or 'Uncategorized',
                'description': description
            })

        # Special pattern for items without em dash
        # Example: "10.99 LUCKY 7 — Tossed in your choice of ONE sauce."
        elif re.match(r'^\d+\.?\d*\s+[A-Z]', line) and current_category:
            # Try to extract price and name from beginning of line
            parts = line.split(' ', 2)
            if len(parts) >= 2:
                try:
                    price = float(parts[0])
                    # Get the rest as item name, clean it up
                    rest = ' '.join(parts[1:])

                    # Split on common separators
                    if '—' in rest:
                        item_name = rest.split('—')[0].strip()
                        description = rest.split('—')[1].strip() if len(rest.split('—')) > 1 else ''
                    elif ' - ' in rest:
                        item_name = rest.split(' - ')[0].strip()
                        description = rest.split(' - ')[1].strip() if len(rest.split(' - ')) > 1 else ''
                    else:
                        # Take first few words as name
                        words = rest.split()
                        if len(words) >= 2:
                            item_name = ' '.join(words[:3])  # Take first 3 words
                            description = ' '.join(words[3:]) if len(words) > 3 else ''
                        else:
                            continue

                    # Clean up
                    item_name = re.sub(r'\s+', ' ', item_name.strip())
                    item_name = item_name.title()

                    # Skip if name is too short or contains unwanted patterns
                    if len(item_name) < 3 or 'add $' in item_name.lower() or 'substitute' in item_name.lower():
                        continue

                    description = re.sub(r'[ÀÁÂÃÄÅàáâãäåÇçÈÉÊËèéêëÌÍÎÏìíîïÑñÒÓÔÕÖØòóôõöøÙÚÛÜùúûüÝýÿŸ]', '', description)
                    description = re.sub(r'\s+', ' ', description)
                    if len(description) > 200:
                        description = description[:200] + "..."

                    menu_items.append({
                        'name': item_name,
                        'price': price,
                        'category': current_category or 'Uncategorized',
                        'description': description
                    })
                except ValueError:
                    continue

    return menu_items

def save_menu_items_json(menu_items, output_file):
    """Save menu items to JSON file for review"""
    with open(output_file, 'w') as f:
        json.dump(menu_items, f, indent=2)
    print(f"Menu items saved to {output_file}")

def main():
    if len(sys.argv) != 2:
        print("Usage: python pdf_menu_processor.py <pdf_file>")
        sys.exit(1)
    
    pdf_file = sys.argv[1]
    
    if not os.path.exists(pdf_file):
        print(f"Error: PDF file '{pdf_file}' not found")
        sys.exit(1)
    
    print(f"Processing PDF: {pdf_file}")
    
    # Extract text from PDF
    text = extract_text_from_pdf(pdf_file)
    if not text:
        print("Failed to extract text from PDF")
        sys.exit(1)
    
    print("Text extracted successfully")
    print(f"Extracted text length: {len(text)} characters")
    
    # Save raw text for debugging
    with open('extracted_text.txt', 'w') as f:
        f.write(text)
    print("Raw extracted text saved to extracted_text.txt")
    
    # Parse menu items
    menu_items = parse_menu_items(text)
    
    print(f"Found {len(menu_items)} potential menu items")
    
    # Save to JSON for review
    save_menu_items_json(menu_items, 'menu_items.json')
    
    # Display first few items for preview
    print("\nFirst 10 items found:")
    for i, item in enumerate(menu_items[:10]):
        print(f"{i+1}. {item['name']} - ${item['price']} ({item['category']})")
    
    if len(menu_items) > 10:
        print(f"... and {len(menu_items) - 10} more items")

if __name__ == "__main__":
    main()
