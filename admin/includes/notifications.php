<?php
/**
 * Notification System for Admin Panel
 * Champions Sports Bar & Grill
 */

class AdminNotifications {
    private $db;
    
    public function __construct($database) {
        $this->db = $database;
    }
    
    /**
     * Get domain renewal notification
     */
    public function getDomainRenewalNotification() {
        try {
            // Get domain settings
            $domainSettings = [];
            $settings = $this->db->fetchAll("SELECT setting_key, setting_value FROM site_settings WHERE setting_key LIKE 'domain_%'");
            
            foreach ($settings as $setting) {
                $domainSettings[$setting['setting_key']] = $setting['setting_value'];
            }
            
            // Check if domain expiry date is set
            if (empty($domainSettings['domain_expiry_date'])) {
                return null;
            }
            
            $expiryDate = $domainSettings['domain_expiry_date'];
            $reminderDays = intval($domainSettings['domain_renewal_reminder_days'] ?? 30);
            $domainName = $domainSettings['domain_name'] ?? 'your domain';
            $registrarUrl = $domainSettings['domain_registrar_login_url'] ?? '';
            $autoRenewal = ($domainSettings['domain_auto_renewal'] ?? '0') === '1';
            
            $expiryTimestamp = strtotime($expiryDate);
            $currentTimestamp = time();
            $daysUntilExpiry = ceil(($expiryTimestamp - $currentTimestamp) / (60 * 60 * 24));
            
            // Don't show notification if auto-renewal is enabled and domain isn't expired
            if ($autoRenewal && $daysUntilExpiry > 0) {
                return null;
            }
            
            // Determine notification type and message
            if ($daysUntilExpiry <= 0) {
                return [
                    'type' => 'danger',
                    'icon' => 'fas fa-times-circle',
                    'title' => 'Domain Expired',
                    'message' => "Your domain <strong>$domainName</strong> expired on " . date('F j, Y', $expiryTimestamp) . ". Please renew immediately to avoid service disruption.",
                    'days' => $daysUntilExpiry,
                    'registrar_url' => $registrarUrl,
                    'priority' => 'high'
                ];
            } elseif ($daysUntilExpiry <= $reminderDays) {
                $urgency = $daysUntilExpiry <= 7 ? 'danger' : 'warning';
                return [
                    'type' => $urgency,
                    'icon' => 'fas fa-exclamation-triangle',
                    'title' => 'Domain Expiring Soon',
                    'message' => "Your domain <strong>$domainName</strong> expires in <strong>$daysUntilExpiry days</strong> (" . date('F j, Y', $expiryTimestamp) . ").",
                    'days' => $daysUntilExpiry,
                    'registrar_url' => $registrarUrl,
                    'priority' => $daysUntilExpiry <= 7 ? 'high' : 'medium'
                ];
            }
            
            return null;
            
        } catch (Exception $e) {
            error_log("Error getting domain renewal notification: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Get all active notifications
     */
    public function getAllNotifications() {
        $notifications = [];
        
        // Domain renewal notification
        $domainNotification = $this->getDomainRenewalNotification();
        if ($domainNotification) {
            $notifications[] = $domainNotification;
        }
        
        // Future: Add other notification types here
        // - SSL certificate expiry
        // - Backup reminders
        // - Security updates
        // - etc.
        
        // Sort by priority (high, medium, low)
        usort($notifications, function($a, $b) {
            $priorities = ['high' => 3, 'medium' => 2, 'low' => 1];
            $aPriority = $priorities[$a['priority'] ?? 'low'];
            $bPriority = $priorities[$b['priority'] ?? 'low'];
            return $bPriority - $aPriority;
        });
        
        return $notifications;
    }
    
    /**
     * Get notification count by type
     */
    public function getNotificationCounts() {
        $notifications = $this->getAllNotifications();
        $counts = [
            'total' => count($notifications),
            'high' => 0,
            'medium' => 0,
            'low' => 0
        ];
        
        foreach ($notifications as $notification) {
            $priority = $notification['priority'] ?? 'low';
            $counts[$priority]++;
        }
        
        return $counts;
    }
    
    /**
     * Check if there are any critical notifications
     */
    public function hasCriticalNotifications() {
        $notifications = $this->getAllNotifications();
        foreach ($notifications as $notification) {
            if (($notification['priority'] ?? 'low') === 'high') {
                return true;
            }
        }
        return false;
    }
}

/**
 * Helper function to get notifications instance
 */
function getNotifications() {
    static $notifications = null;
    if ($notifications === null) {
        $notifications = new AdminNotifications(getDB());
    }
    return $notifications;
}

/**
 * Helper function to render notification alert
 */
function renderNotificationAlert($notification) {
    if (!$notification) return '';
    
    $html = '<div class="alert alert-' . htmlspecialchars($notification['type']) . ' d-flex align-items-center" role="alert">';
    $html .= '<i class="' . htmlspecialchars($notification['icon']) . ' me-3 fs-4"></i>';
    $html .= '<div class="flex-grow-1">';
    $html .= '<h5 class="alert-heading mb-1">' . htmlspecialchars($notification['title']) . '</h5>';
    $html .= '<p class="mb-2">' . $notification['message'] . '</p>';
    $html .= '<div class="d-flex gap-2">';
    
    if (!empty($notification['registrar_url'])) {
        $html .= '<a href="' . htmlspecialchars($notification['registrar_url']) . '" target="_blank" class="btn btn-sm btn-' . htmlspecialchars($notification['type']) . '">';
        $html .= '<i class="fas fa-external-link-alt me-1"></i>Renew Domain</a>';
    }
    
    $html .= '<a href="settings.php#domain-management" class="btn btn-sm btn-outline-' . htmlspecialchars($notification['type']) . '">';
    $html .= '<i class="fas fa-cog me-1"></i>Update Settings</a>';
    $html .= '</div></div>';
    
    if (isset($notification['days'])) {
        $html .= '<div class="text-end">';
        $html .= '<div class="fs-2 fw-bold">' . abs($notification['days']) . '</div>';
        $html .= '<small class="text-muted">' . ($notification['days'] <= 0 ? 'days ago' : 'days left') . '</small>';
        $html .= '</div>';
    }
    
    $html .= '</div>';
    
    return $html;
}
?>
