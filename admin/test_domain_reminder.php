<?php
/**
 * Test Domain Renewal Reminder
 * Champions Sports Bar & Grill
 */

require_once 'config/database.php';

try {
    $db = getDB();
    
    echo "Testing Domain Renewal Reminder System\n";
    echo "=====================================\n\n";
    
    // Set test domain expiry date (30 days from now)
    $testExpiryDate = date('Y-m-d', strtotime('+25 days'));
    
    echo "Setting test domain expiry date to: $testExpiryDate\n";
    
    // Update domain settings for testing
    $testSettings = [
        'domain_name' => 'champions-sportsgrill.com',
        'domain_registrar' => 'GoDaddy',
        'domain_expiry_date' => $testExpiryDate,
        'domain_renewal_reminder_days' => '30',
        'domain_auto_renewal' => '0',
        'domain_registrar_login_url' => 'https://account.godaddy.com'
    ];
    
    foreach ($testSettings as $key => $value) {
        $db->query(
            "UPDATE site_settings SET setting_value = ? WHERE setting_key = ?",
            [$value, $key]
        );
    }
    
    echo "✓ Test settings applied\n\n";
    
    // Test the notification system
    require_once 'includes/notifications.php';
    
    $notifications = getNotifications();
    $domainNotification = $notifications->getDomainRenewalNotification();
    
    if ($domainNotification) {
        echo "Domain Renewal Notification Generated:\n";
        echo "-------------------------------------\n";
        echo "Type: " . $domainNotification['type'] . "\n";
        echo "Title: " . $domainNotification['title'] . "\n";
        echo "Message: " . strip_tags($domainNotification['message']) . "\n";
        echo "Days until expiry: " . $domainNotification['days'] . "\n";
        echo "Priority: " . $domainNotification['priority'] . "\n";
        
        if (!empty($domainNotification['registrar_url'])) {
            echo "Registrar URL: " . $domainNotification['registrar_url'] . "\n";
        }
    } else {
        echo "No domain renewal notification generated.\n";
    }
    
    echo "\n";
    
    // Test different scenarios
    echo "Testing Different Scenarios:\n";
    echo "============================\n\n";
    
    $scenarios = [
        ['days' => 7, 'desc' => '7 days until expiry (should be danger)'],
        ['days' => 15, 'desc' => '15 days until expiry (should be warning)'],
        ['days' => 45, 'desc' => '45 days until expiry (should be no alert)'],
        ['days' => -5, 'desc' => '5 days past expiry (should be danger)']
    ];
    
    foreach ($scenarios as $scenario) {
        $testDate = date('Y-m-d', strtotime("+{$scenario['days']} days"));
        
        $db->query(
            "UPDATE site_settings SET setting_value = ? WHERE setting_key = 'domain_expiry_date'",
            [$testDate]
        );
        
        $testNotification = $notifications->getDomainRenewalNotification();
        
        echo "Scenario: " . $scenario['desc'] . "\n";
        echo "Date: $testDate\n";
        
        if ($testNotification) {
            echo "Alert Type: " . $testNotification['type'] . "\n";
            echo "Priority: " . $testNotification['priority'] . "\n";
        } else {
            echo "No alert generated\n";
        }
        echo "\n";
    }
    
    // Test auto-renewal scenario
    echo "Testing Auto-Renewal Scenario:\n";
    echo "==============================\n";
    
    $db->query(
        "UPDATE site_settings SET setting_value = '1' WHERE setting_key = 'domain_auto_renewal'"
    );
    
    $db->query(
        "UPDATE site_settings SET setting_value = ? WHERE setting_key = 'domain_expiry_date'",
        [date('Y-m-d', strtotime('+15 days'))]
    );
    
    $autoRenewalNotification = $notifications->getDomainRenewalNotification();
    
    if ($autoRenewalNotification) {
        echo "Alert generated despite auto-renewal being enabled\n";
    } else {
        echo "✓ No alert generated when auto-renewal is enabled (correct behavior)\n";
    }
    
    // Reset to original test settings
    $db->query(
        "UPDATE site_settings SET setting_value = '0' WHERE setting_key = 'domain_auto_renewal'"
    );
    
    $db->query(
        "UPDATE site_settings SET setting_value = ? WHERE setting_key = 'domain_expiry_date'",
        [$testExpiryDate]
    );
    
    echo "\n✓ Test completed successfully!\n";
    echo "\nTo see the reminder in action:\n";
    echo "1. Visit: http://localhost:8000/admin/dashboard.php\n";
    echo "2. Visit: http://localhost:8000/admin/settings.php\n";
    echo "\nTo reset, clear the domain_expiry_date in settings.\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    exit(1);
}
?>
