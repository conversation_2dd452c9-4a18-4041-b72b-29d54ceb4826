<?php
/**
 * Champions Sports Bar & Grill - Change Password
 */

$pageTitle = 'Change Password';
require_once 'includes/header.php';

$auth->requireLogin();

$db = getDB();
$message = '';
$messageType = '';

// Get current user data
$currentUser = $auth->getCurrentUser();

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $csrfToken = $_POST['csrf_token'] ?? '';
    
    if (!verifyCSRFToken($csrfToken)) {
        $message = 'Invalid security token.';
        $messageType = 'danger';
    } else {
        $currentPassword = $_POST['current_password'] ?? '';
        $newPassword = $_POST['new_password'] ?? '';
        $confirmPassword = $_POST['confirm_password'] ?? '';
        
        // Validate passwords
        if (empty($currentPassword) || empty($newPassword) || empty($confirmPassword)) {
            $message = 'All password fields are required.';
            $messageType = 'danger';
        } elseif ($newPassword !== $confirmPassword) {
            $message = 'New passwords do not match.';
            $messageType = 'danger';
        } elseif (strlen($newPassword) < 8) {
            $message = 'New password must be at least 8 characters long.';
            $messageType = 'danger';
        } elseif ($currentPassword === $newPassword) {
            $message = 'New password must be different from current password.';
            $messageType = 'danger';
        } else {
            // Use auth class method to change password
            $result = $auth->changePassword($currentUser['id'], $currentPassword, $newPassword);
            
            $message = $result['message'];
            $messageType = $result['success'] ? 'success' : 'danger';
            
            // Clear form on success
            if ($result['success']) {
                $_POST = [];
            }
        }
    }
}
?>

<?php if ($message): ?>
    <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show">
        <?php echo htmlspecialchars($message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h2 mb-0">
                    <i class="fas fa-key me-2"></i>
                    Change Password
                </h1>
                <p class="text-muted">Update your account password for security</p>
            </div>
            <a href="profile.php" class="btn btn-outline-secondary">
                <i class="fas fa-user me-1"></i> Back to Profile
            </a>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-shield-alt me-2"></i>
                    Security Settings
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="" id="changePasswordForm">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    
                    <div class="mb-4">
                        <label for="current_password" class="form-label required">Current Password</label>
                        <div class="input-group">
                            <input type="password" 
                                   class="form-control" 
                                   id="current_password" 
                                   name="current_password" 
                                   required
                                   autocomplete="current-password">
                            <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('current_password')">
                                <i class="fas fa-eye" id="current_password_icon"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="new_password" class="form-label required">New Password</label>
                        <div class="input-group">
                            <input type="password" 
                                   class="form-control" 
                                   id="new_password" 
                                   name="new_password" 
                                   minlength="8"
                                   required
                                   autocomplete="new-password">
                            <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('new_password')">
                                <i class="fas fa-eye" id="new_password_icon"></i>
                            </button>
                        </div>
                        <div class="form-text">
                            <i class="fas fa-info-circle me-1"></i>
                            Password must be at least 8 characters long
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <label for="confirm_password" class="form-label required">Confirm New Password</label>
                        <div class="input-group">
                            <input type="password" 
                                   class="form-control" 
                                   id="confirm_password" 
                                   name="confirm_password" 
                                   minlength="8"
                                   required
                                   autocomplete="new-password">
                            <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('confirm_password')">
                                <i class="fas fa-eye" id="confirm_password_icon"></i>
                            </button>
                        </div>
                        <div id="password_match_feedback" class="form-text"></div>
                    </div>
                    
                    <!-- Password Strength Indicator -->
                    <div class="mb-4">
                        <label class="form-label">Password Strength</label>
                        <div class="progress" style="height: 8px;">
                            <div class="progress-bar" 
                                 id="password_strength_bar" 
                                 role="progressbar" 
                                 style="width: 0%"
                                 aria-valuenow="0" 
                                 aria-valuemin="0" 
                                 aria-valuemax="100"></div>
                        </div>
                        <small id="password_strength_text" class="text-muted">Enter a password to see strength</small>
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-key me-2"></i>
                            Change Password
                        </button>
                        <a href="dashboard.php" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-1"></i>
                            Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Security Tips -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-lightbulb me-2"></i>
                    Password Security Tips
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        Use at least 8 characters
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        Include uppercase and lowercase letters
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        Add numbers and special characters
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        Avoid common words or personal information
                    </li>
                    <li class="mb-0">
                        <i class="fas fa-check text-success me-2"></i>
                        Use a unique password for this account
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
// Password visibility toggle
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const icon = document.getElementById(fieldId + '_icon');
    
    if (field.type === 'password') {
        field.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        field.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}

// Password strength checker
function checkPasswordStrength(password) {
    let strength = 0;
    let feedback = [];
    
    // Length check
    if (password.length >= 8) strength += 20;
    else feedback.push('At least 8 characters');
    
    // Uppercase check
    if (/[A-Z]/.test(password)) strength += 20;
    else feedback.push('Uppercase letter');
    
    // Lowercase check
    if (/[a-z]/.test(password)) strength += 20;
    else feedback.push('Lowercase letter');
    
    // Number check
    if (/[0-9]/.test(password)) strength += 20;
    else feedback.push('Number');
    
    // Special character check
    if (/[^A-Za-z0-9]/.test(password)) strength += 20;
    else feedback.push('Special character');
    
    return { strength, feedback };
}

// Update password strength indicator
function updatePasswordStrength() {
    const password = document.getElementById('new_password').value;
    const strengthBar = document.getElementById('password_strength_bar');
    const strengthText = document.getElementById('password_strength_text');
    
    if (!password) {
        strengthBar.style.width = '0%';
        strengthBar.className = 'progress-bar';
        strengthText.textContent = 'Enter a password to see strength';
        strengthText.className = 'text-muted';
        return;
    }
    
    const result = checkPasswordStrength(password);
    const strength = result.strength;
    
    strengthBar.style.width = strength + '%';
    strengthBar.setAttribute('aria-valuenow', strength);
    
    if (strength < 40) {
        strengthBar.className = 'progress-bar bg-danger';
        strengthText.textContent = 'Weak - Missing: ' + result.feedback.join(', ');
        strengthText.className = 'text-danger';
    } else if (strength < 80) {
        strengthBar.className = 'progress-bar bg-warning';
        strengthText.textContent = 'Fair - Missing: ' + result.feedback.join(', ');
        strengthText.className = 'text-warning';
    } else {
        strengthBar.className = 'progress-bar bg-success';
        strengthText.textContent = 'Strong password';
        strengthText.className = 'text-success';
    }
}

// Password confirmation validation
function validatePasswordMatch() {
    const newPassword = document.getElementById('new_password').value;
    const confirmPassword = document.getElementById('confirm_password').value;
    const feedback = document.getElementById('password_match_feedback');
    
    if (!confirmPassword) {
        feedback.textContent = '';
        feedback.className = 'form-text';
        return;
    }
    
    if (newPassword === confirmPassword) {
        feedback.textContent = '✓ Passwords match';
        feedback.className = 'form-text text-success';
        document.getElementById('confirm_password').setCustomValidity('');
    } else {
        feedback.textContent = '✗ Passwords do not match';
        feedback.className = 'form-text text-danger';
        document.getElementById('confirm_password').setCustomValidity('Passwords do not match');
    }
}

// Event listeners
document.addEventListener('DOMContentLoaded', function() {
    const newPassword = document.getElementById('new_password');
    const confirmPassword = document.getElementById('confirm_password');
    
    newPassword.addEventListener('input', function() {
        updatePasswordStrength();
        validatePasswordMatch();
    });
    
    confirmPassword.addEventListener('input', validatePasswordMatch);
    
    // Form submission validation
    document.getElementById('changePasswordForm').addEventListener('submit', function(e) {
        const newPass = newPassword.value;
        const confirmPass = confirmPassword.value;
        
        if (newPass !== confirmPass) {
            e.preventDefault();
            alert('Passwords do not match. Please check and try again.');
            return false;
        }
        
        if (newPass.length < 8) {
            e.preventDefault();
            alert('Password must be at least 8 characters long.');
            return false;
        }
    });
});
</script>

<?php require_once 'includes/footer.php'; ?>
