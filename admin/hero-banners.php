<?php
/**
 * Champions Sports Bar & Grill - Hero Banners Management
 */

$pageTitle = 'Hero Banners';
require_once 'includes/header.php';

$auth->requirePermission('manage_content');

$db = getDB();
$message = '';
$messageType = '';

/**
 * Handle hero banner image upload
 */
function handleHeroBannerUpload($file) {
    try {
        // Validate file
        $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        $maxSize = 10 * 1024 * 1024; // 10MB for hero banners

        if (!in_array($file['type'], $allowedTypes)) {
            return ['success' => false, 'message' => 'Invalid file type. Please upload a JPEG, PNG, GIF, or WebP image.'];
        }

        if ($file['size'] > $maxSize) {
            return ['success' => false, 'message' => 'File too large. Maximum size is 10MB.'];
        }

        // Validate image dimensions
        $imageInfo = getimagesize($file['tmp_name']);
        if (!$imageInfo) {
            return ['success' => false, 'message' => 'Invalid image file.'];
        }

        $width = $imageInfo[0];
        $height = $imageInfo[1];

        // Recommend minimum dimensions for hero banners
        if ($width < 1200 || $height < 600) {
            return ['success' => false, 'message' => 'Image too small. Recommended minimum size is 1200x600px for hero banners.'];
        }

        // Set upload directory
        $uploadDir = '../assets/images/heroes/';

        // Create directory if it doesn't exist
        if (!file_exists($uploadDir)) {
            if (!mkdir($uploadDir, 0755, true)) {
                return ['success' => false, 'message' => 'Failed to create upload directory.'];
            }
        }

        // Generate unique filename
        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        $filename = 'hero_' . uniqid() . '_' . time() . '.' . $extension;
        $filepath = $uploadDir . $filename;

        // Move uploaded file
        if (!move_uploaded_file($file['tmp_name'], $filepath)) {
            return ['success' => false, 'message' => 'Failed to move uploaded file.'];
        }

        // Return relative URL for database storage
        $relativeUrl = 'assets/images/heroes/' . $filename;

        return [
            'success' => true,
            'message' => 'Image uploaded successfully',
            'filename' => $filename,
            'url' => $relativeUrl,
            'full_url' => '/' . $relativeUrl,
            'dimensions' => $width . 'x' . $height
        ];

    } catch (Exception $e) {
        return ['success' => false, 'message' => $e->getMessage()];
    }
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $csrfToken = $_POST['csrf_token'] ?? '';
    
    if (!verifyCSRFToken($csrfToken)) {
        $message = 'Invalid security token.';
        $messageType = 'danger';
    } else {
        switch ($action) {
            case 'add_banner':
            case 'edit_banner':
                $id = $action === 'edit_banner' ? (int)$_POST['id'] : null;
                $title = sanitize($_POST['title']);
                $subtitle = sanitize($_POST['subtitle']);
                $description = sanitize($_POST['description']);
                $imageUrl = sanitize($_POST['image_url']);

                // Handle file upload if provided
                if (isset($_FILES['image_file']) && $_FILES['image_file']['error'] === UPLOAD_ERR_OK) {
                    $uploadResult = handleHeroBannerUpload($_FILES['image_file']);
                    if ($uploadResult['success']) {
                        $imageUrl = $uploadResult['url'];
                    } else {
                        $message = 'Upload error: ' . $uploadResult['message'];
                        $messageType = 'danger';
                        break;
                    }
                }
                $buttonText = sanitize($_POST['button_text']);
                $buttonUrl = sanitize($_POST['button_url']);
                $isActive = isset($_POST['is_active']) ? 1 : 0;
                $sortOrder = (int)($_POST['sort_order'] ?? 0);
                
                $data = [
                    'title' => $title,
                    'subtitle' => $subtitle,
                    'description' => $description,
                    'image_url' => $imageUrl,
                    'button_text' => $buttonText,
                    'button_url' => $buttonUrl,
                    'is_active' => $isActive,
                    'sort_order' => $sortOrder
                ];
                
                if ($action === 'add_banner') {
                    $data['created_by'] = $currentUser['id'];
                    $bannerId = $db->insert('hero_banners', $data);
                    logActivity($currentUser['id'], 'hero_banner_create', 'hero_banners', $bannerId);
                    $message = 'Hero banner added successfully!';
                } else {
                    $db->update('hero_banners', $data, 'id = :id', ['id' => $id]);
                    logActivity($currentUser['id'], 'hero_banner_update', 'hero_banners', $id);
                    $message = 'Hero banner updated successfully!';
                }
                
                $messageType = 'success';
                break;
                
            case 'delete_banner':
                $id = (int)$_POST['id'];
                $db->delete('hero_banners', 'id = :id', ['id' => $id]);
                logActivity($currentUser['id'], 'hero_banner_delete', 'hero_banners', $id);
                $message = 'Hero banner deleted successfully!';
                $messageType = 'success';
                break;
                
            case 'toggle_status':
                $id = (int)$_POST['id'];
                $banner = $db->fetch("SELECT is_active FROM hero_banners WHERE id = :id", ['id' => $id]);
                if ($banner) {
                    $newStatus = $banner['is_active'] ? 0 : 1;
                    $db->update('hero_banners', ['is_active' => $newStatus], 'id = :id', ['id' => $id]);
                    logActivity($currentUser['id'], 'hero_banner_toggle', 'hero_banners', $id);
                    $message = 'Banner status updated successfully!';
                    $messageType = 'success';
                }
                break;
        }
    }
}

// Get action from URL
$urlAction = $_GET['action'] ?? 'list';
$editId = $_GET['id'] ?? null;

// Get banner for editing
$editBanner = null;
if ($urlAction === 'edit' && $editId) {
    $editBanner = $db->fetch("SELECT * FROM hero_banners WHERE id = :id", ['id' => $editId]);
    if (!$editBanner) {
        $urlAction = 'list';
    }
}

// Get all banners
$banners = $db->fetchAll("SELECT * FROM hero_banners ORDER BY sort_order ASC, created_at DESC");
?>

<?php if ($message): ?>
    <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show">
        <?php echo htmlspecialchars($message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h2 mb-0">
                    <i class="fas fa-image me-2"></i>
                    Hero Banners
                </h1>
                <p class="text-muted">Manage homepage hero banners and sliders</p>
            </div>
            <?php if ($urlAction === 'list'): ?>
                <a href="?action=add" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i> Add New Banner
                </a>
            <?php else: ?>
                <a href="hero-banners.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-1"></i> Back to List
                </a>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php if ($urlAction === 'list'): ?>
    <!-- Banners List -->
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">Current Hero Banners</h5>
        </div>
        <div class="card-body">
            <?php if (empty($banners)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-image fa-3x text-muted mb-3"></i>
                    <h5>No Hero Banners</h5>
                    <p class="text-muted">Create your first hero banner to get started.</p>
                    <a href="?action=add" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i> Add Hero Banner
                    </a>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Preview</th>
                                <th>Title</th>
                                <th>Status</th>
                                <th>Order</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody class="sortable" data-table="hero_banners">
                            <?php foreach ($banners as $banner): ?>
                                <tr data-id="<?php echo $banner['id']; ?>">
                                    <td>
                                        <img src="<?php echo htmlspecialchars($banner['image_url']); ?>" 
                                             alt="Banner preview" 
                                             class="img-thumbnail" 
                                             style="width: 80px; height: 50px; object-fit: cover;">
                                    </td>
                                    <td>
                                        <strong><?php echo htmlspecialchars($banner['title']); ?></strong>
                                        <?php if ($banner['subtitle']): ?>
                                            <br><small class="text-muted"><?php echo htmlspecialchars($banner['subtitle']); ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <form method="POST" class="d-inline">
                                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                            <input type="hidden" name="action" value="toggle_status">
                                            <input type="hidden" name="id" value="<?php echo $banner['id']; ?>">
                                            <button type="submit" 
                                                    class="btn btn-sm <?php echo $banner['is_active'] ? 'btn-success' : 'btn-secondary'; ?>">
                                                <i class="fas <?php echo $banner['is_active'] ? 'fa-check' : 'fa-times'; ?>"></i>
                                                <?php echo $banner['is_active'] ? 'Active' : 'Inactive'; ?>
                                            </button>
                                        </form>
                                    </td>
                                    <td>
                                        <i class="fas fa-grip-vertical sort-handle text-muted me-2"></i>
                                        <?php echo $banner['sort_order']; ?>
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            <?php echo date('M j, Y', strtotime($banner['created_at'])); ?>
                                        </small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="?action=edit&id=<?php echo $banner['id']; ?>" 
                                               class="btn btn-outline-primary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <form method="POST" class="d-inline">
                                                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                                <input type="hidden" name="action" value="delete_banner">
                                                <input type="hidden" name="id" value="<?php echo $banner['id']; ?>">
                                                <button type="submit" 
                                                        class="btn btn-outline-danger btn-delete">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>

<?php else: ?>
    <!-- Add/Edit Banner Form -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <?php echo $urlAction === 'add' ? 'Add New' : 'Edit'; ?> Hero Banner
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="" enctype="multipart/form-data">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        <input type="hidden" name="action" value="<?php echo $urlAction === 'add' ? 'add_banner' : 'edit_banner'; ?>">
                        <?php if ($editBanner): ?>
                            <input type="hidden" name="id" value="<?php echo $editBanner['id']; ?>">
                        <?php endif; ?>
                        
                        <div class="row">
                            <div class="col-md-8 mb-3">
                                <label for="title" class="form-label required">Title</label>
                                <input type="text" 
                                       class="form-control" 
                                       id="title" 
                                       name="title" 
                                       value="<?php echo htmlspecialchars($editBanner['title'] ?? ''); ?>" 
                                       required>
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="sort_order" class="form-label">Sort Order</label>
                                <input type="number" 
                                       class="form-control" 
                                       id="sort_order" 
                                       name="sort_order" 
                                       value="<?php echo $editBanner['sort_order'] ?? 0; ?>" 
                                       min="0">
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="subtitle" class="form-label">Subtitle</label>
                            <input type="text" 
                                   class="form-control" 
                                   id="subtitle" 
                                   name="subtitle" 
                                   value="<?php echo htmlspecialchars($editBanner['subtitle'] ?? ''); ?>">
                        </div>
                        
                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control" 
                                      id="description" 
                                      name="description" 
                                      rows="3"><?php echo htmlspecialchars($editBanner['description'] ?? ''); ?></textarea>
                        </div>
                        
                        <!-- Image Upload/URL Section -->
                        <div class="mb-3">
                            <label class="form-label required">Hero Banner Image</label>

                            <!-- Upload/URL Toggle -->
                            <div class="btn-group mb-3" role="group" aria-label="Image input method">
                                <input type="radio" class="btn-check" name="image_input_method" id="method_upload" value="upload" checked>
                                <label class="btn btn-outline-primary" for="method_upload">
                                    <i class="fas fa-upload me-1"></i>Upload File
                                </label>

                                <input type="radio" class="btn-check" name="image_input_method" id="method_url" value="url">
                                <label class="btn btn-outline-primary" for="method_url">
                                    <i class="fas fa-link me-1"></i>Use URL
                                </label>
                            </div>

                            <!-- File Upload Section -->
                            <div id="upload_section" class="upload-section">
                                <div class="mb-3">
                                    <label for="image_file" class="form-label">Choose Image File</label>
                                    <input type="file"
                                           class="form-control"
                                           id="image_file"
                                           name="image_file"
                                           accept="image/jpeg,image/png,image/gif,image/webp">
                                    <div class="form-text">
                                        <strong>Recommended:</strong> 1920x1080px or larger, JPEG/PNG/WebP format, max 10MB<br>
                                        <strong>Minimum:</strong> 1200x600px for optimal display
                                    </div>
                                </div>

                                <!-- Upload Preview -->
                                <div id="upload_preview" class="upload-preview mt-2" style="display: none;">
                                    <div class="border rounded p-3 bg-light">
                                        <div class="d-flex align-items-center">
                                            <img id="preview_image" src="" alt="Preview" class="img-thumbnail me-3" style="max-width: 150px; max-height: 100px;">
                                            <div>
                                                <h6 class="mb-1" id="preview_filename"></h6>
                                                <small class="text-muted" id="preview_details"></small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- URL Input Section -->
                            <div id="url_section" class="url-section" style="display: none;">
                                <div class="mb-3">
                                    <label for="image_url" class="form-label">Image URL</label>
                                    <input type="url"
                                           class="form-control"
                                           id="image_url"
                                           name="image_url"
                                           value="<?php echo htmlspecialchars($editBanner['image_url'] ?? ''); ?>"
                                           placeholder="https://example.com/hero-image.jpg">
                                    <div class="form-text">Enter the full URL to the hero banner image</div>
                                </div>

                                <!-- URL Preview -->
                                <div id="url_preview" class="file-preview mt-2"></div>
                            </div>

                            <!-- Current Image Display (for edit mode) -->
                            <?php if ($editBanner && !empty($editBanner['image_url'])): ?>
                            <div class="current-image mt-3">
                                <label class="form-label">Current Image:</label>
                                <div class="border rounded p-2 bg-light">
                                    <img src="/<?php echo htmlspecialchars($editBanner['image_url']); ?>"
                                         alt="Current hero banner"
                                         class="img-thumbnail"
                                         style="max-width: 300px; max-height: 200px;">
                                    <div class="mt-2">
                                        <small class="text-muted">
                                            Current: <?php echo htmlspecialchars($editBanner['image_url']); ?>
                                        </small>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="button_text" class="form-label">Button Text</label>
                                <input type="text" 
                                       class="form-control" 
                                       id="button_text" 
                                       name="button_text" 
                                       value="<?php echo htmlspecialchars($editBanner['button_text'] ?? ''); ?>"
                                       placeholder="e.g., View Menu">
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="button_url" class="form-label">Button URL</label>
                                <input type="url" 
                                       class="form-control" 
                                       id="button_url" 
                                       name="button_url" 
                                       value="<?php echo htmlspecialchars($editBanner['button_url'] ?? ''); ?>"
                                       placeholder="https://example.com/menu">
                            </div>
                        </div>
                        
                        <div class="mb-3 form-check">
                            <input type="checkbox" 
                                   class="form-check-input" 
                                   id="is_active" 
                                   name="is_active" 
                                   <?php echo ($editBanner['is_active'] ?? 1) ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="is_active">
                                Active (show on website)
                            </label>
                        </div>
                        
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i> 
                                <?php echo $urlAction === 'add' ? 'Add' : 'Update'; ?> Banner
                            </button>
                            <a href="hero-banners.php" class="btn btn-secondary">Cancel</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">Banner Guidelines</h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled small">
                        <li class="mb-2">
                            <i class="fas fa-image text-info me-1"></i>
                            <strong>Image Size:</strong> 1920x1080px recommended
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-file-image text-warning me-1"></i>
                            <strong>Format:</strong> JPG, PNG, or WebP
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-compress text-success me-1"></i>
                            <strong>File Size:</strong> Under 1MB for fast loading
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-eye text-primary me-1"></i>
                            <strong>Content:</strong> Ensure text is readable on image
                        </li>
                        <li class="mb-0">
                            <i class="fas fa-mobile-alt text-secondary me-1"></i>
                            <strong>Mobile:</strong> Test how it looks on mobile devices
                        </li>
                    </ul>
                </div>
            </div>
            
            <?php if ($editBanner && $editBanner['image_url']): ?>
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="card-title mb-0">Current Image</h6>
                </div>
                <div class="card-body p-0">
                    <img src="<?php echo htmlspecialchars($editBanner['image_url']); ?>" 
                         alt="Current banner" 
                         class="img-fluid">
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
<?php endif; ?>

<script>
// Image input method toggle
document.addEventListener('DOMContentLoaded', function() {
    const methodUpload = document.getElementById('method_upload');
    const methodUrl = document.getElementById('method_url');
    const uploadSection = document.getElementById('upload_section');
    const urlSection = document.getElementById('url_section');
    const imageFile = document.getElementById('image_file');
    const imageUrl = document.getElementById('image_url');

    // Set initial state based on existing data
    <?php if ($editBanner && !empty($editBanner['image_url'])): ?>
    // If editing and has image URL, default to URL method
    methodUrl.checked = true;
    toggleSections();
    <?php endif; ?>

    // Toggle sections based on selected method
    function toggleSections() {
        if (methodUpload.checked) {
            uploadSection.style.display = 'block';
            urlSection.style.display = 'none';
            imageUrl.removeAttribute('required');
            // Don't make file required if editing existing banner
            <?php if (!$editBanner): ?>
            imageFile.setAttribute('required', 'required');
            <?php endif; ?>
        } else {
            uploadSection.style.display = 'none';
            urlSection.style.display = 'block';
            imageFile.removeAttribute('required');
            imageUrl.setAttribute('required', 'required');
        }
    }

    // Event listeners for method toggle
    methodUpload.addEventListener('change', toggleSections);
    methodUrl.addEventListener('change', toggleSections);

    // File upload preview
    if (imageFile) {
        imageFile.addEventListener('change', function() {
            const file = this.files[0];
            const preview = document.getElementById('upload_preview');
            const previewImage = document.getElementById('preview_image');
            const previewFilename = document.getElementById('preview_filename');
            const previewDetails = document.getElementById('preview_details');

            if (file) {
                // Validate file type
                const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
                if (!allowedTypes.includes(file.type)) {
                    alert('Please select a valid image file (JPEG, PNG, GIF, or WebP)');
                    this.value = '';
                    preview.style.display = 'none';
                    return;
                }

                // Validate file size (10MB)
                if (file.size > 10 * 1024 * 1024) {
                    alert('File size must be less than 10MB');
                    this.value = '';
                    preview.style.display = 'none';
                    return;
                }

                // Create preview
                const reader = new FileReader();
                reader.onload = function(e) {
                    previewImage.src = e.target.result;
                    previewFilename.textContent = file.name;
                    previewDetails.textContent = `${(file.size / 1024 / 1024).toFixed(2)} MB`;
                    preview.style.display = 'block';

                    // Check image dimensions
                    const img = new Image();
                    img.onload = function() {
                        const dimensions = `${this.width}x${this.height}px`;
                        previewDetails.textContent = `${(file.size / 1024 / 1024).toFixed(2)} MB • ${dimensions}`;

                        if (this.width < 1200 || this.height < 600) {
                            previewDetails.innerHTML += ' <span class="text-warning">⚠️ Below recommended size</span>';
                        }
                    };
                    img.src = e.target.result;
                };
                reader.readAsDataURL(file);
            } else {
                preview.style.display = 'none';
            }
        });
    }

    // URL preview
    if (imageUrl) {
        imageUrl.addEventListener('input', function() {
            const url = this.value;
            const preview = document.getElementById('url_preview');

            if (url && (url.startsWith('http://') || url.startsWith('https://'))) {
                preview.innerHTML = '<img src="' + url + '" class="img-thumbnail" style="max-width: 300px;" onerror="this.style.display=\'none\'">';
            } else {
                preview.innerHTML = '';
            }
        });

        // Trigger preview for existing URL
        if (imageUrl.value) {
            imageUrl.dispatchEvent(new Event('input'));
        }
    }

    // Initialize sections
    toggleSections();
});

// Initialize sortable if on list page
<?php if ($urlAction === 'list' && !empty($banners)): ?>
$(document).ready(function() {
    if ($.fn.sortable) {
        $('.sortable').sortable({
            handle: '.sort-handle',
            update: function(event, ui) {
                const order = $(this).sortable('toArray', {attribute: 'data-id'});
                
                $.ajax({
                    url: 'ajax/update-order.php',
                    method: 'POST',
                    data: {
                        table: 'hero_banners',
                        order: order,
                        csrf_token: $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(response) {
                        if (response.success) {
                            ChampionsAdmin.showAlert('Banner order updated successfully', 'success');
                        }
                    }
                });
            }
        });
    }
});
<?php endif; ?>
</script>

<?php require_once 'includes/footer.php'; ?>
