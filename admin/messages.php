<?php
/**
 * Champions Sports Bar & Grill - Contact Messages Management
 */

$pageTitle = 'Contact Messages';
require_once 'includes/header.php';

$auth->requireLogin();

$db = getDB();
$message = '';
$messageType = '';

// Handle actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $csrfToken = $_POST['csrf_token'] ?? '';
    
    if (!verifyCSRFToken($csrfToken)) {
        $message = 'Invalid security token.';
        $messageType = 'danger';
    } else {
        switch ($action) {
            case 'update_status':
                $messageId = intval($_POST['message_id']);
                $status = sanitize($_POST['status']);
                
                if (in_array($status, ['new', 'read', 'replied', 'archived'])) {
                    $updateData = ['status' => $status];
                    
                    if ($status === 'replied') {
                        $updateData['replied_at'] = date('Y-m-d H:i:s');
                        $updateData['replied_by'] = $auth->getCurrentUser()['id'];
                    }
                    
                    $db->update('contact_messages', $updateData, 'id = :id', ['id' => $messageId]);
                    
                    // Log activity
                    logActivity($auth->getCurrentUser()['id'], 'message_status_update', 'contact_messages', $messageId);
                    
                    $message = 'Message status updated successfully.';
                    $messageType = 'success';
                }
                break;
                
            case 'bulk_action':
                $selectedMessages = $_POST['selected_messages'] ?? [];
                $bulkAction = $_POST['bulk_action'] ?? '';
                
                if (!empty($selectedMessages) && in_array($bulkAction, ['mark_read', 'mark_replied', 'archive', 'delete'])) {
                    $count = 0;
                    foreach ($selectedMessages as $messageId) {
                        $messageId = intval($messageId);
                        
                        switch ($bulkAction) {
                            case 'mark_read':
                                $db->update('contact_messages', ['status' => 'read'], 'id = :id', ['id' => $messageId]);
                                break;
                            case 'mark_replied':
                                $db->update('contact_messages', [
                                    'status' => 'replied',
                                    'replied_at' => date('Y-m-d H:i:s'),
                                    'replied_by' => $auth->getCurrentUser()['id']
                                ], 'id = :id', ['id' => $messageId]);
                                break;
                            case 'archive':
                                $db->update('contact_messages', ['status' => 'archived'], 'id = :id', ['id' => $messageId]);
                                break;
                            case 'delete':
                                $db->delete('contact_messages', 'id = :id', ['id' => $messageId]);
                                break;
                        }
                        $count++;
                    }
                    
                    // Log bulk activity
                    logActivity($auth->getCurrentUser()['id'], 'bulk_message_action', 'contact_messages');
                    
                    $message = "Bulk action applied to $count messages.";
                    $messageType = 'success';
                }
                break;
                
            case 'add_note':
                $messageId = intval($_POST['message_id']);
                $note = sanitize($_POST['note']);
                
                if (!empty($note)) {
                    // Get current message
                    $currentMessage = $db->fetch("SELECT * FROM contact_messages WHERE id = :id", ['id' => $messageId]);
                    if ($currentMessage) {
                        $existingNotes = $currentMessage['notes'] ?? '';
                        $newNote = date('Y-m-d H:i:s') . ' - ' . $auth->getCurrentUser()['first_name'] . ': ' . $note;
                        $updatedNotes = $existingNotes ? $existingNotes . "\n\n" . $newNote : $newNote;
                        
                        $db->update('contact_messages', ['notes' => $updatedNotes], 'id = :id', ['id' => $messageId]);
                        
                        $message = 'Note added successfully.';
                        $messageType = 'success';
                    }
                }
                break;
        }
    }
}

// Get filter parameters
$status = $_GET['status'] ?? 'all';
$search = $_GET['search'] ?? '';
$page = max(1, intval($_GET['page'] ?? 1));
$perPage = 20;
$offset = ($page - 1) * $perPage;

// Build query
$whereConditions = [];
$params = [];

if ($status !== 'all') {
    $whereConditions[] = "status = :status";
    $params['status'] = $status;
}

if (!empty($search)) {
    $whereConditions[] = "(name LIKE :search OR email LIKE :search OR subject LIKE :search OR message LIKE :search)";
    $params['search'] = "%$search%";
}

$whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

// Get total count
$totalQuery = "SELECT COUNT(*) as count FROM contact_messages $whereClause";
$totalMessages = $db->fetch($totalQuery, $params)['count'];
$totalPages = ceil($totalMessages / $perPage);

// Get messages
$messagesQuery = "
    SELECT cm.*, au.first_name as replied_by_name, au.last_name as replied_by_lastname
    FROM contact_messages cm
    LEFT JOIN admin_users au ON cm.replied_by = au.id
    $whereClause
    ORDER BY cm.created_at DESC
    LIMIT $perPage OFFSET $offset
";

$messages = $db->fetchAll($messagesQuery, $params);

// Get status counts for filter tabs
$statusCounts = [
    'all' => $db->fetch("SELECT COUNT(*) as count FROM contact_messages")['count'],
    'new' => $db->fetch("SELECT COUNT(*) as count FROM contact_messages WHERE status = 'new'")['count'],
    'read' => $db->fetch("SELECT COUNT(*) as count FROM contact_messages WHERE status = 'read'")['count'],
    'replied' => $db->fetch("SELECT COUNT(*) as count FROM contact_messages WHERE status = 'replied'")['count'],
    'archived' => $db->fetch("SELECT COUNT(*) as count FROM contact_messages WHERE status = 'archived'")['count']
];
?>

<?php if ($message): ?>
    <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show">
        <?php echo htmlspecialchars($message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h2 mb-0">
                    <i class="fas fa-envelope me-2"></i>
                    Contact Messages
                </h1>
                <p class="text-muted">Manage customer inquiries and contact form submissions</p>
            </div>
            <div class="d-flex gap-2">
                <button class="btn btn-outline-secondary" onclick="refreshPage()">
                    <i class="fas fa-sync-alt me-1"></i> Refresh
                </button>
                <div class="dropdown">
                    <button class="btn btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-download me-1"></i> Export
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" onclick="exportMessages('csv')">
                            <i class="fas fa-file-csv me-1"></i> Export as CSV
                        </a></li>
                        <li><a class="dropdown-item" href="#" onclick="exportMessages('excel')">
                            <i class="fas fa-file-excel me-1"></i> Export as Excel
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters and Search -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="" class="row g-3">
            <div class="col-md-3">
                <label for="status" class="form-label">Status</label>
                <select class="form-select" id="status" name="status" onchange="this.form.submit()">
                    <option value="all" <?php echo $status === 'all' ? 'selected' : ''; ?>>
                        All Messages (<?php echo $statusCounts['all']; ?>)
                    </option>
                    <option value="new" <?php echo $status === 'new' ? 'selected' : ''; ?>>
                        New (<?php echo $statusCounts['new']; ?>)
                    </option>
                    <option value="read" <?php echo $status === 'read' ? 'selected' : ''; ?>>
                        Read (<?php echo $statusCounts['read']; ?>)
                    </option>
                    <option value="replied" <?php echo $status === 'replied' ? 'selected' : ''; ?>>
                        Replied (<?php echo $statusCounts['replied']; ?>)
                    </option>
                    <option value="archived" <?php echo $status === 'archived' ? 'selected' : ''; ?>>
                        Archived (<?php echo $statusCounts['archived']; ?>)
                    </option>
                </select>
            </div>
            <div class="col-md-6">
                <label for="search" class="form-label">Search</label>
                <div class="input-group">
                    <input type="text" class="form-control" id="search" name="search" 
                           value="<?php echo htmlspecialchars($search); ?>" 
                           placeholder="Search by name, email, subject, or message...">
                    <button class="btn btn-outline-secondary" type="submit">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <a href="messages.php" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-1"></i> Clear Filters
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Messages List -->
<div class="card">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                Messages (<?php echo number_format($totalMessages); ?> total)
            </h5>
            
            <!-- Bulk Actions -->
            <div class="d-flex gap-2">
                <form method="POST" action="" id="bulkActionForm" class="d-flex gap-2">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    <input type="hidden" name="action" value="bulk_action">
                    
                    <select class="form-select form-select-sm" name="bulk_action" style="width: auto;">
                        <option value="">Bulk Actions</option>
                        <option value="mark_read">Mark as Read</option>
                        <option value="mark_replied">Mark as Replied</option>
                        <option value="archive">Archive</option>
                        <option value="delete">Delete</option>
                    </select>
                    
                    <button type="submit" class="btn btn-sm btn-outline-primary" onclick="return confirmBulkAction()">
                        Apply
                    </button>
                </form>
            </div>
        </div>
    </div>
    
    <div class="card-body p-0">
        <?php if (empty($messages)): ?>
            <div class="text-center py-5">
                <i class="fas fa-envelope-open-text fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No messages found</h5>
                <p class="text-muted">
                    <?php if (!empty($search) || $status !== 'all'): ?>
                        Try adjusting your filters or search terms.
                    <?php else: ?>
                        Contact form submissions will appear here.
                    <?php endif; ?>
                </p>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th width="40">
                                <input type="checkbox" class="form-check-input" id="selectAll" onchange="toggleSelectAll()">
                            </th>
                            <th>Contact Info</th>
                            <th>Subject & Message</th>
                            <th width="120">Status</th>
                            <th width="150">Date</th>
                            <th width="100">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($messages as $msg): ?>
                            <tr class="<?php echo $msg['status'] === 'new' ? 'table-warning' : ''; ?>">
                                <td>
                                    <input type="checkbox" class="form-check-input message-checkbox" 
                                           name="selected_messages[]" value="<?php echo $msg['id']; ?>" 
                                           form="bulkActionForm">
                                </td>
                                <td>
                                    <div class="d-flex align-items-start">
                                        <div class="me-2">
                                            <?php if ($msg['status'] === 'new'): ?>
                                                <i class="fas fa-circle text-warning" title="New message"></i>
                                            <?php elseif ($msg['status'] === 'replied'): ?>
                                                <i class="fas fa-reply text-success" title="Replied"></i>
                                            <?php elseif ($msg['status'] === 'archived'): ?>
                                                <i class="fas fa-archive text-secondary" title="Archived"></i>
                                            <?php else: ?>
                                                <i class="fas fa-envelope-open text-info" title="Read"></i>
                                            <?php endif; ?>
                                        </div>
                                        <div>
                                            <div class="fw-bold"><?php echo htmlspecialchars($msg['name']); ?></div>
                                            <div class="text-muted small">
                                                <i class="fas fa-envelope me-1"></i>
                                                <a href="mailto:<?php echo htmlspecialchars($msg['email']); ?>">
                                                    <?php echo htmlspecialchars($msg['email']); ?>
                                                </a>
                                            </div>
                                            <?php if ($msg['phone']): ?>
                                                <div class="text-muted small">
                                                    <i class="fas fa-phone me-1"></i>
                                                    <a href="tel:<?php echo htmlspecialchars($msg['phone']); ?>">
                                                        <?php echo htmlspecialchars($msg['phone']); ?>
                                                    </a>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="fw-bold mb-1">
                                        <?php echo htmlspecialchars($msg['subject'] ?: 'No Subject'); ?>
                                    </div>
                                    <div class="text-muted small">
                                        <?php 
                                        $preview = strip_tags($msg['message']);
                                        echo htmlspecialchars(strlen($preview) > 100 ? substr($preview, 0, 100) . '...' : $preview); 
                                        ?>
                                    </div>
                                </td>
                                <td>
                                    <form method="POST" action="" class="d-inline">
                                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                        <input type="hidden" name="action" value="update_status">
                                        <input type="hidden" name="message_id" value="<?php echo $msg['id']; ?>">
                                        
                                        <select class="form-select form-select-sm" name="status" onchange="this.form.submit()">
                                            <option value="new" <?php echo $msg['status'] === 'new' ? 'selected' : ''; ?>>New</option>
                                            <option value="read" <?php echo $msg['status'] === 'read' ? 'selected' : ''; ?>>Read</option>
                                            <option value="replied" <?php echo $msg['status'] === 'replied' ? 'selected' : ''; ?>>Replied</option>
                                            <option value="archived" <?php echo $msg['status'] === 'archived' ? 'selected' : ''; ?>>Archived</option>
                                        </select>
                                    </form>
                                </td>
                                <td>
                                    <div class="small">
                                        <?php echo date('M j, Y', strtotime($msg['created_at'])); ?>
                                    </div>
                                    <div class="text-muted small">
                                        <?php echo date('g:i A', strtotime($msg['created_at'])); ?>
                                    </div>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary" 
                                                onclick="viewMessage(<?php echo $msg['id']; ?>)"
                                                title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <a href="mailto:<?php echo htmlspecialchars($msg['email']); ?>?subject=Re: <?php echo urlencode($msg['subject']); ?>" 
                                           class="btn btn-outline-success" title="Reply">
                                            <i class="fas fa-reply"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
    
    <!-- Pagination -->
    <?php if ($totalPages > 1): ?>
        <div class="card-footer">
            <nav aria-label="Messages pagination">
                <ul class="pagination pagination-sm mb-0 justify-content-center">
                    <?php if ($page > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?php echo $page - 1; ?>&status=<?php echo urlencode($status); ?>&search=<?php echo urlencode($search); ?>">
                                <i class="fas fa-chevron-left"></i>
                            </a>
                        </li>
                    <?php endif; ?>
                    
                    <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                        <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                            <a class="page-link" href="?page=<?php echo $i; ?>&status=<?php echo urlencode($status); ?>&search=<?php echo urlencode($search); ?>">
                                <?php echo $i; ?>
                            </a>
                        </li>
                    <?php endfor; ?>
                    
                    <?php if ($page < $totalPages): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?php echo $page + 1; ?>&status=<?php echo urlencode($status); ?>&search=<?php echo urlencode($search); ?>">
                                <i class="fas fa-chevron-right"></i>
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
            </nav>
            
            <div class="text-center mt-2">
                <small class="text-muted">
                    Showing <?php echo number_format($offset + 1); ?> to <?php echo number_format(min($offset + $perPage, $totalMessages)); ?> 
                    of <?php echo number_format($totalMessages); ?> messages
                </small>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Message Detail Modal -->
<div class="modal fade" id="messageModal" tabindex="-1" aria-labelledby="messageModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="messageModalLabel">Message Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="messageModalBody">
                <!-- Content loaded via AJAX -->
                <div class="text-center py-4">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-success" id="replyButton" onclick="replyToMessage()">
                    <i class="fas fa-reply me-1"></i> Reply
                </button>
            </div>
        </div>
    </div>
</div>

<script>
let currentMessageId = null;
let currentMessageEmail = null;
let currentMessageSubject = null;

// Toggle select all checkboxes
function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.message-checkbox');

    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
}

// Confirm bulk action
function confirmBulkAction() {
    const selectedCheckboxes = document.querySelectorAll('.message-checkbox:checked');
    const bulkAction = document.querySelector('select[name="bulk_action"]').value;

    if (selectedCheckboxes.length === 0) {
        alert('Please select at least one message.');
        return false;
    }

    if (!bulkAction) {
        alert('Please select an action.');
        return false;
    }

    const actionText = {
        'mark_read': 'mark as read',
        'mark_replied': 'mark as replied',
        'archive': 'archive',
        'delete': 'delete'
    };

    const confirmText = `Are you sure you want to ${actionText[bulkAction]} ${selectedCheckboxes.length} message(s)?`;

    if (bulkAction === 'delete') {
        return confirm(confirmText + '\n\nThis action cannot be undone.');
    }

    return confirm(confirmText);
}

// View message details
function viewMessage(messageId) {
    currentMessageId = messageId;

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('messageModal'));
    modal.show();

    // Load message details via AJAX
    fetch(`message-details.php?id=${messageId}`)
        .then(response => response.text())
        .then(html => {
            document.getElementById('messageModalBody').innerHTML = html;

            // Extract email and subject for reply button
            const emailElement = document.querySelector('#messageEmail');
            const subjectElement = document.querySelector('#messageSubject');

            if (emailElement) currentMessageEmail = emailElement.textContent;
            if (subjectElement) currentMessageSubject = subjectElement.textContent;
        })
        .catch(error => {
            console.error('Error loading message details:', error);
            document.getElementById('messageModalBody').innerHTML =
                '<div class="alert alert-danger">Error loading message details. Please try again.</div>';
        });
}

// Reply to message
function replyToMessage() {
    if (currentMessageEmail) {
        const subject = currentMessageSubject ? `Re: ${currentMessageSubject}` : 'Re: Your message';
        const mailtoLink = `mailto:${currentMessageEmail}?subject=${encodeURIComponent(subject)}`;
        window.open(mailtoLink);

        // Mark as replied
        if (currentMessageId) {
            markAsReplied(currentMessageId);
        }
    }
}

// Mark message as replied
function markAsReplied(messageId) {
    const formData = new FormData();
    formData.append('csrf_token', '<?php echo generateCSRFToken(); ?>');
    formData.append('action', 'update_status');
    formData.append('message_id', messageId);
    formData.append('status', 'replied');

    fetch('messages.php', {
        method: 'POST',
        body: formData
    }).then(() => {
        // Refresh page to show updated status
        setTimeout(() => {
            window.location.reload();
        }, 1000);
    });
}

// Refresh page
function refreshPage() {
    window.location.reload();
}

// Export messages
function exportMessages(format) {
    const params = new URLSearchParams(window.location.search);
    params.set('export', format);

    window.open(`export-messages.php?${params.toString()}`);
}

// Auto-refresh every 5 minutes for new messages
setInterval(() => {
    // Only refresh if we're on the first page and showing new messages
    const urlParams = new URLSearchParams(window.location.search);
    const status = urlParams.get('status') || 'all';
    const page = urlParams.get('page') || '1';

    if (page === '1' && (status === 'all' || status === 'new')) {
        // Check for new messages without full page reload
        fetch('check-new-messages.php')
            .then(response => response.json())
            .then(data => {
                if (data.hasNewMessages) {
                    // Show notification
                    const notification = document.createElement('div');
                    notification.className = 'alert alert-info alert-dismissible fade show position-fixed';
                    notification.style.cssText = 'top: 80px; right: 20px; z-index: 1050; max-width: 300px;';
                    notification.innerHTML = `
                        <i class="fas fa-envelope me-2"></i>
                        New messages received.
                        <a href="#" onclick="window.location.reload()" class="alert-link">Refresh to view</a>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    `;
                    document.body.appendChild(notification);

                    // Auto-remove after 10 seconds
                    setTimeout(() => {
                        if (notification.parentNode) {
                            notification.remove();
                        }
                    }, 10000);
                }
            })
            .catch(error => console.error('Error checking for new messages:', error));
    }
}, 300000); // 5 minutes

// Update select all checkbox when individual checkboxes change
document.addEventListener('DOMContentLoaded', function() {
    const checkboxes = document.querySelectorAll('.message-checkbox');
    const selectAll = document.getElementById('selectAll');

    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const checkedCount = document.querySelectorAll('.message-checkbox:checked').length;
            const totalCount = checkboxes.length;

            selectAll.checked = checkedCount === totalCount;
            selectAll.indeterminate = checkedCount > 0 && checkedCount < totalCount;
        });
    });
});
</script>

<?php require_once 'includes/footer.php'; ?>
