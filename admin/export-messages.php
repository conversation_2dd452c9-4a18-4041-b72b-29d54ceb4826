<?php
/**
 * Champions Sports Bar & Grill - Export Messages
 */

require_once 'includes/auth.php';
require_once 'config/database.php';

$auth = new Auth();
$auth->requireLogin();

$db = getDB();

// Get export format
$format = $_GET['export'] ?? 'csv';
$status = $_GET['status'] ?? 'all';
$search = $_GET['search'] ?? '';

// Build query
$whereConditions = [];
$params = [];

if ($status !== 'all') {
    $whereConditions[] = "status = :status";
    $params['status'] = $status;
}

if (!empty($search)) {
    $whereConditions[] = "(name LIKE :search OR email LIKE :search OR subject LIKE :search OR message LIKE :search)";
    $params['search'] = "%$search%";
}

$whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

// Get messages
$messagesQuery = "
    SELECT cm.*, au.first_name as replied_by_name, au.last_name as replied_by_lastname
    FROM contact_messages cm
    LEFT JOIN admin_users au ON cm.replied_by = au.id
    $whereClause
    ORDER BY cm.created_at DESC
";

$messages = $db->fetchAll($messagesQuery, $params);

// Log export activity
logActivity($auth->getCurrentUser()['id'], 'messages_export', 'contact_messages');

if ($format === 'csv') {
    // CSV Export
    $filename = 'contact_messages_' . date('Y-m-d_H-i-s') . '.csv';
    
    header('Content-Type: text/csv');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Cache-Control: no-cache, must-revalidate');
    header('Expires: Sat, 26 Jul 1997 05:00:00 GMT');
    
    $output = fopen('php://output', 'w');
    
    // CSV Headers
    fputcsv($output, [
        'ID',
        'Name',
        'Email',
        'Phone',
        'Subject',
        'Message',
        'Status',
        'IP Address',
        'Submitted Date',
        'Replied Date',
        'Replied By',
        'Notes'
    ]);
    
    // CSV Data
    foreach ($messages as $message) {
        fputcsv($output, [
            $message['id'],
            $message['name'],
            $message['email'],
            $message['phone'],
            $message['subject'],
            $message['message'],
            $message['status'],
            $message['ip_address'],
            $message['created_at'],
            $message['replied_at'],
            $message['replied_by_name'] ? $message['replied_by_name'] . ' ' . $message['replied_by_lastname'] : '',
            $message['notes']
        ]);
    }
    
    fclose($output);
    
} elseif ($format === 'excel') {
    // Excel Export (HTML table that Excel can read)
    $filename = 'contact_messages_' . date('Y-m-d_H-i-s') . '.xls';
    
    header('Content-Type: application/vnd.ms-excel');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Cache-Control: no-cache, must-revalidate');
    header('Expires: Sat, 26 Jul 1997 05:00:00 GMT');
    
    echo '<html>';
    echo '<head><meta charset="UTF-8"></head>';
    echo '<body>';
    echo '<table border="1">';
    
    // Headers
    echo '<tr>';
    echo '<th>ID</th>';
    echo '<th>Name</th>';
    echo '<th>Email</th>';
    echo '<th>Phone</th>';
    echo '<th>Subject</th>';
    echo '<th>Message</th>';
    echo '<th>Status</th>';
    echo '<th>IP Address</th>';
    echo '<th>Submitted Date</th>';
    echo '<th>Replied Date</th>';
    echo '<th>Replied By</th>';
    echo '<th>Notes</th>';
    echo '</tr>';
    
    // Data
    foreach ($messages as $message) {
        echo '<tr>';
        echo '<td>' . htmlspecialchars($message['id']) . '</td>';
        echo '<td>' . htmlspecialchars($message['name']) . '</td>';
        echo '<td>' . htmlspecialchars($message['email']) . '</td>';
        echo '<td>' . htmlspecialchars($message['phone']) . '</td>';
        echo '<td>' . htmlspecialchars($message['subject']) . '</td>';
        echo '<td>' . htmlspecialchars($message['message']) . '</td>';
        echo '<td>' . htmlspecialchars($message['status']) . '</td>';
        echo '<td>' . htmlspecialchars($message['ip_address']) . '</td>';
        echo '<td>' . htmlspecialchars($message['created_at']) . '</td>';
        echo '<td>' . htmlspecialchars($message['replied_at']) . '</td>';
        echo '<td>' . htmlspecialchars($message['replied_by_name'] ? $message['replied_by_name'] . ' ' . $message['replied_by_lastname'] : '') . '</td>';
        echo '<td>' . htmlspecialchars($message['notes']) . '</td>';
        echo '</tr>';
    }
    
    echo '</table>';
    echo '</body>';
    echo '</html>';
    
} else {
    // Invalid format
    header('HTTP/1.1 400 Bad Request');
    echo 'Invalid export format';
}

exit;
?>
