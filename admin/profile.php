<?php
/**
 * Champions Sports Bar & Grill - Admin Profile Management
 */

$pageTitle = 'My Profile';
require_once 'includes/header.php';

$auth->requireLogin();

$db = getDB();
$message = '';
$messageType = '';

// Get current user data
$currentUser = $auth->getCurrentUser();

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $csrfToken = $_POST['csrf_token'] ?? '';
    
    if (!verifyCSRFToken($csrfToken)) {
        $message = 'Invalid security token.';
        $messageType = 'danger';
    } else {
        switch ($action) {
            case 'update_profile':
                $firstName = sanitize($_POST['first_name']);
                $lastName = sanitize($_POST['last_name']);
                $email = sanitize($_POST['email']);
                $username = sanitize($_POST['username']);
                
                // Validate required fields
                if (empty($firstName) || empty($lastName) || empty($email) || empty($username)) {
                    $message = 'All fields are required.';
                    $messageType = 'danger';
                    break;
                }
                
                // Validate email format
                if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                    $message = 'Please enter a valid email address.';
                    $messageType = 'danger';
                    break;
                }
                
                // Check if username or email already exists (excluding current user)
                $existing = $db->fetch(
                    "SELECT id FROM admin_users WHERE (username = :username OR email = :email) AND id != :current_id",
                    [
                        'username' => $username,
                        'email' => $email,
                        'current_id' => $currentUser['id']
                    ]
                );
                
                if ($existing) {
                    $message = 'Username or email already exists.';
                    $messageType = 'danger';
                    break;
                }
                
                // Update user profile
                $updateData = [
                    'first_name' => $firstName,
                    'last_name' => $lastName,
                    'email' => $email,
                    'username' => $username,
                    'updated_at' => date('Y-m-d H:i:s')
                ];
                
                $db->update('admin_users', $updateData, 'id = :id', ['id' => $currentUser['id']]);
                
                // Update session data
                $_SESSION['admin_username'] = $username;
                $_SESSION['admin_name'] = $firstName . ' ' . $lastName;
                
                // Log activity
                logActivity($currentUser['id'], 'profile_update');
                
                $message = 'Profile updated successfully!';
                $messageType = 'success';
                
                // Refresh current user data
                $currentUser = $auth->getCurrentUser();
                break;
                
            case 'change_password':
                $currentPassword = $_POST['current_password'] ?? '';
                $newPassword = $_POST['new_password'] ?? '';
                $confirmPassword = $_POST['confirm_password'] ?? '';
                
                // Validate passwords
                if (empty($currentPassword) || empty($newPassword) || empty($confirmPassword)) {
                    $message = 'All password fields are required.';
                    $messageType = 'danger';
                    break;
                }
                
                if ($newPassword !== $confirmPassword) {
                    $message = 'New passwords do not match.';
                    $messageType = 'danger';
                    break;
                }
                
                if (strlen($newPassword) < 8) {
                    $message = 'New password must be at least 8 characters long.';
                    $messageType = 'danger';
                    break;
                }
                
                // Use auth class method to change password
                $result = $auth->changePassword($currentUser['id'], $currentPassword, $newPassword);
                
                $message = $result['message'];
                $messageType = $result['success'] ? 'success' : 'danger';
                break;
        }
    }
}

// Get user activity stats
$activityStats = [
    'total_logins' => $db->fetch("SELECT COUNT(*) as count FROM admin_activity_log WHERE user_id = :id AND action = 'login'", ['id' => $currentUser['id']])['count'],
    'last_login' => $currentUser['last_login'],
    'account_created' => $currentUser['created_at'],
    'recent_activities' => $db->fetchAll(
        "SELECT action, table_name, created_at FROM admin_activity_log 
         WHERE user_id = :id 
         ORDER BY created_at DESC 
         LIMIT 10",
        ['id' => $currentUser['id']]
    )
];
?>

<?php if ($message): ?>
    <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show">
        <?php echo htmlspecialchars($message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<div class="row mb-4">
    <div class="col-12">
        <h1 class="h2 mb-0">
            <i class="fas fa-user-edit me-2"></i>
            My Profile
        </h1>
        <p class="text-muted">Manage your account information and settings</p>
    </div>
</div>

<div class="row">
    <!-- Profile Information -->
    <div class="col-lg-8">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user me-2"></i>
                    Profile Information
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    <input type="hidden" name="action" value="update_profile">
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="first_name" class="form-label required">First Name</label>
                            <input type="text" 
                                   class="form-control" 
                                   id="first_name" 
                                   name="first_name" 
                                   value="<?php echo htmlspecialchars($currentUser['first_name']); ?>" 
                                   required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="last_name" class="form-label required">Last Name</label>
                            <input type="text" 
                                   class="form-control" 
                                   id="last_name" 
                                   name="last_name" 
                                   value="<?php echo htmlspecialchars($currentUser['last_name']); ?>" 
                                   required>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="username" class="form-label required">Username</label>
                            <input type="text" 
                                   class="form-control" 
                                   id="username" 
                                   name="username" 
                                   value="<?php echo htmlspecialchars($currentUser['username']); ?>" 
                                   required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label required">Email Address</label>
                            <input type="email" 
                                   class="form-control" 
                                   id="email" 
                                   name="email" 
                                   value="<?php echo htmlspecialchars($currentUser['email']); ?>" 
                                   required>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Role</label>
                            <input type="text" 
                                   class="form-control" 
                                   value="<?php echo ucfirst($currentUser['role']); ?>" 
                                   readonly>
                            <div class="form-text">Your role cannot be changed from this page</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Account Status</label>
                            <input type="text" 
                                   class="form-control" 
                                   value="<?php echo $currentUser['is_active'] ? 'Active' : 'Inactive'; ?>" 
                                   readonly>
                        </div>
                    </div>
                    
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i> Update Profile
                        </button>
                        <a href="dashboard.php" class="btn btn-secondary">Cancel</a>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Change Password -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-key me-2"></i>
                    Change Password
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    <input type="hidden" name="action" value="change_password">
                    
                    <div class="mb-3">
                        <label for="current_password" class="form-label required">Current Password</label>
                        <input type="password" 
                               class="form-control" 
                               id="current_password" 
                               name="current_password" 
                               required>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="new_password" class="form-label required">New Password</label>
                            <input type="password" 
                                   class="form-control" 
                                   id="new_password" 
                                   name="new_password" 
                                   minlength="8"
                                   required>
                            <div class="form-text">Minimum 8 characters</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="confirm_password" class="form-label required">Confirm New Password</label>
                            <input type="password" 
                                   class="form-control" 
                                   id="confirm_password" 
                                   name="confirm_password" 
                                   minlength="8"
                                   required>
                        </div>
                    </div>
                    
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-key me-1"></i> Change Password
                    </button>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Account Information Sidebar -->
    <div class="col-lg-4">
        <!-- User Avatar Card -->
        <div class="card mb-4 text-center">
            <div class="card-body">
                <div class="profile-avatar mx-auto">
                    <?php echo strtoupper(substr($currentUser['first_name'], 0, 1) . substr($currentUser['last_name'], 0, 1)); ?>
                </div>
                <h5 class="card-title mb-1">
                    <?php echo htmlspecialchars($currentUser['first_name'] . ' ' . $currentUser['last_name']); ?>
                </h5>
                <p class="text-muted mb-2">@<?php echo htmlspecialchars($currentUser['username']); ?></p>
                <span class="badge bg-<?php echo $currentUser['role'] === 'admin' ? 'danger' : ($currentUser['role'] === 'manager' ? 'warning' : 'info'); ?>">
                    <?php echo ucfirst($currentUser['role']); ?>
                </span>
            </div>
        </div>

        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    Account Information
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <strong>Account Created:</strong><br>
                    <span class="text-muted">
                        <?php echo date('F j, Y \a\t g:i A', strtotime($activityStats['account_created'])); ?>
                    </span>
                </div>
                
                <div class="mb-3">
                    <strong>Last Login:</strong><br>
                    <span class="text-muted">
                        <?php 
                        if ($activityStats['last_login']) {
                            echo date('F j, Y \a\t g:i A', strtotime($activityStats['last_login']));
                        } else {
                            echo 'Never';
                        }
                        ?>
                    </span>
                </div>
                
                <div class="mb-3">
                    <strong>Total Logins:</strong><br>
                    <span class="text-muted"><?php echo number_format($activityStats['total_logins']); ?></span>
                </div>
                
                <div class="mb-0">
                    <strong>User ID:</strong><br>
                    <span class="text-muted">#<?php echo $currentUser['id']; ?></span>
                </div>
            </div>
        </div>
        
        <!-- Recent Activity -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-history me-2"></i>
                    Recent Activity
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($activityStats['recent_activities'])): ?>
                    <p class="text-muted text-center py-3">No recent activity</p>
                <?php else: ?>
                    <div class="activity-list">
                        <?php foreach ($activityStats['recent_activities'] as $activity): ?>
                            <div class="activity-item mb-2 pb-2 border-bottom">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <small class="fw-bold">
                                            <?php echo ucwords(str_replace('_', ' ', $activity['action'])); ?>
                                        </small>
                                        <?php if ($activity['table_name']): ?>
                                            <br><small class="text-muted">
                                                <?php echo ucwords(str_replace('_', ' ', $activity['table_name'])); ?>
                                            </small>
                                        <?php endif; ?>
                                    </div>
                                    <small class="text-muted">
                                        <?php echo date('M j, g:i A', strtotime($activity['created_at'])); ?>
                                    </small>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<script>
// Password confirmation validation
document.addEventListener('DOMContentLoaded', function() {
    const newPassword = document.getElementById('new_password');
    const confirmPassword = document.getElementById('confirm_password');
    
    function validatePasswords() {
        if (newPassword.value && confirmPassword.value) {
            if (newPassword.value !== confirmPassword.value) {
                confirmPassword.setCustomValidity('Passwords do not match');
            } else {
                confirmPassword.setCustomValidity('');
            }
        }
    }
    
    if (newPassword && confirmPassword) {
        newPassword.addEventListener('input', validatePasswords);
        confirmPassword.addEventListener('input', validatePasswords);
    }
});
</script>

<?php require_once 'includes/footer.php'; ?>
