<?php
/**
 * Add Domain Settings to Database
 * Champions Sports Bar & Grill
 */

require_once 'config/database.php';

try {
    $db = getDB();
    
    echo "Adding domain settings to database...\n";
    
    $domainSettings = [
        ['domain_name', 'champions-sportsgrill.com', 'text', 'Primary domain name'],
        ['domain_registrar', '', 'text', 'Domain registrar name'],
        ['domain_expiry_date', '', 'text', 'Domain expiration date (YYYY-MM-DD)'],
        ['domain_renewal_reminder_days', '30', 'number', 'Days before expiry to show reminder'],
        ['domain_auto_renewal', '0', 'boolean', 'Is auto-renewal enabled'],
        ['domain_registrar_login_url', '', 'text', 'Registrar login URL for quick access']
    ];
    
    $added = 0;
    $existing = 0;
    
    foreach ($domainSettings as $setting) {
        $check = $db->fetch(
            "SELECT id FROM site_settings WHERE setting_key = ?", 
            [$setting[0]]
        );
        
        if (!$check) {
            $db->query(
                "INSERT INTO site_settings (setting_key, setting_value, setting_type, description) VALUES (?, ?, ?, ?)",
                [$setting[0], $setting[1], $setting[2], $setting[3]]
            );
            echo "✓ Added: " . $setting[0] . "\n";
            $added++;
        } else {
            echo "- Already exists: " . $setting[0] . "\n";
            $existing++;
        }
    }
    
    echo "\nSummary:\n";
    echo "- Added: $added settings\n";
    echo "- Already existed: $existing settings\n";
    echo "\nDomain settings update completed successfully!\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    exit(1);
}
?>
