<?php
/**
 * Champions Sports Bar & Grill - Message Details AJAX Handler
 */

require_once 'includes/auth.php';
require_once 'config/database.php';

$auth = new Auth();
$auth->requireLogin();

$db = getDB();
$messageId = intval($_GET['id'] ?? 0);

if (!$messageId) {
    echo '<div class="alert alert-danger">Invalid message ID.</div>';
    exit;
}

// Get message details
$message = $db->fetch("
    SELECT cm.*, au.first_name as replied_by_name, au.last_name as replied_by_lastname
    FROM contact_messages cm
    LEFT JOIN admin_users au ON cm.replied_by = au.id
    WHERE cm.id = :id
", ['id' => $messageId]);

if (!$message) {
    echo '<div class="alert alert-danger">Message not found.</div>';
    exit;
}

// Mark as read if it's new
if ($message['status'] === 'new') {
    $db->update('contact_messages', ['status' => 'read'], 'id = :id', ['id' => $messageId]);
    logActivity($auth->getCurrentUser()['id'], 'message_read', 'contact_messages', $messageId);
}
?>

<div class="row">
    <div class="col-md-8">
        <!-- Message Content -->
        <div class="mb-4">
            <h6 class="text-muted mb-2">Subject</h6>
            <h5 id="messageSubject"><?php echo htmlspecialchars($message['subject'] ?: 'No Subject'); ?></h5>
        </div>
        
        <div class="mb-4">
            <h6 class="text-muted mb-2">Message</h6>
            <div class="border rounded p-3 bg-light">
                <?php echo nl2br(htmlspecialchars($message['message'])); ?>
            </div>
        </div>
        
        <?php if ($message['notes']): ?>
        <div class="mb-4">
            <h6 class="text-muted mb-2">Internal Notes</h6>
            <div class="border rounded p-3 bg-warning bg-opacity-10">
                <?php echo nl2br(htmlspecialchars($message['notes'])); ?>
            </div>
        </div>
        <?php endif; ?>
        
        <!-- Add Note Form -->
        <div class="mb-4">
            <h6 class="text-muted mb-2">Add Internal Note</h6>
            <form method="POST" action="messages.php" class="d-flex gap-2">
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                <input type="hidden" name="action" value="add_note">
                <input type="hidden" name="message_id" value="<?php echo $message['id']; ?>">
                
                <div class="flex-grow-1">
                    <textarea class="form-control" name="note" rows="2" 
                              placeholder="Add a note for internal reference..."></textarea>
                </div>
                <div>
                    <button type="submit" class="btn btn-outline-primary">
                        <i class="fas fa-plus"></i> Add Note
                    </button>
                </div>
            </form>
        </div>
    </div>
    
    <div class="col-md-4">
        <!-- Contact Information -->
        <div class="card mb-3">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-user me-1"></i> Contact Information
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <strong>Name:</strong><br>
                    <?php echo htmlspecialchars($message['name']); ?>
                </div>
                
                <div class="mb-3">
                    <strong>Email:</strong><br>
                    <a href="mailto:<?php echo htmlspecialchars($message['email']); ?>" id="messageEmail">
                        <?php echo htmlspecialchars($message['email']); ?>
                    </a>
                </div>
                
                <?php if ($message['phone']): ?>
                <div class="mb-3">
                    <strong>Phone:</strong><br>
                    <a href="tel:<?php echo htmlspecialchars($message['phone']); ?>">
                        <?php echo htmlspecialchars($message['phone']); ?>
                    </a>
                </div>
                <?php endif; ?>
                
                <div class="mb-3">
                    <strong>Status:</strong><br>
                    <span class="badge bg-<?php 
                        echo $message['status'] === 'new' ? 'warning' : 
                            ($message['status'] === 'replied' ? 'success' : 
                            ($message['status'] === 'archived' ? 'secondary' : 'info')); 
                    ?>">
                        <?php echo ucfirst($message['status']); ?>
                    </span>
                </div>
                
                <div class="mb-0">
                    <strong>Received:</strong><br>
                    <small class="text-muted">
                        <?php echo date('F j, Y \a\t g:i A', strtotime($message['created_at'])); ?>
                    </small>
                </div>
            </div>
        </div>
        
        <!-- Technical Information -->
        <div class="card mb-3">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-info-circle me-1"></i> Technical Details
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-2">
                    <strong>Message ID:</strong><br>
                    <small class="text-muted">#<?php echo $message['id']; ?></small>
                </div>
                
                <?php if ($message['ip_address']): ?>
                <div class="mb-2">
                    <strong>IP Address:</strong><br>
                    <small class="text-muted"><?php echo htmlspecialchars($message['ip_address']); ?></small>
                </div>
                <?php endif; ?>
                
                <?php if ($message['user_agent']): ?>
                <div class="mb-2">
                    <strong>Browser:</strong><br>
                    <small class="text-muted" title="<?php echo htmlspecialchars($message['user_agent']); ?>">
                        <?php 
                        // Extract browser name from user agent
                        $userAgent = $message['user_agent'];
                        if (strpos($userAgent, 'Chrome') !== false) echo 'Chrome';
                        elseif (strpos($userAgent, 'Firefox') !== false) echo 'Firefox';
                        elseif (strpos($userAgent, 'Safari') !== false) echo 'Safari';
                        elseif (strpos($userAgent, 'Edge') !== false) echo 'Edge';
                        else echo 'Other';
                        ?>
                    </small>
                </div>
                <?php endif; ?>
                
                <?php if ($message['replied_at']): ?>
                <div class="mb-0">
                    <strong>Replied:</strong><br>
                    <small class="text-muted">
                        <?php echo date('F j, Y \a\t g:i A', strtotime($message['replied_at'])); ?>
                        <?php if ($message['replied_by_name']): ?>
                            <br>by <?php echo htmlspecialchars($message['replied_by_name'] . ' ' . $message['replied_by_lastname']); ?>
                        <?php endif; ?>
                    </small>
                </div>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-bolt me-1"></i> Quick Actions
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="mailto:<?php echo htmlspecialchars($message['email']); ?>?subject=Re: <?php echo urlencode($message['subject']); ?>" 
                       class="btn btn-success btn-sm">
                        <i class="fas fa-reply me-1"></i> Reply via Email
                    </a>
                    
                    <?php if ($message['phone']): ?>
                    <a href="tel:<?php echo htmlspecialchars($message['phone']); ?>" 
                       class="btn btn-info btn-sm">
                        <i class="fas fa-phone me-1"></i> Call Customer
                    </a>
                    <?php endif; ?>
                    
                    <form method="POST" action="messages.php" class="d-inline">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        <input type="hidden" name="action" value="update_status">
                        <input type="hidden" name="message_id" value="<?php echo $message['id']; ?>">
                        <input type="hidden" name="status" value="archived">
                        
                        <button type="submit" class="btn btn-outline-secondary btn-sm w-100">
                            <i class="fas fa-archive me-1"></i> Archive Message
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Message Templates (for future enhancement) -->
<div class="mt-4">
    <h6 class="text-muted mb-2">Quick Reply Templates</h6>
    <div class="btn-group-sm" role="group">
        <button type="button" class="btn btn-outline-secondary btn-sm" 
                onclick="useTemplate('thank_you')">
            Thank You
        </button>
        <button type="button" class="btn btn-outline-secondary btn-sm" 
                onclick="useTemplate('more_info')">
            Need More Info
        </button>
        <button type="button" class="btn btn-outline-secondary btn-sm" 
                onclick="useTemplate('resolved')">
            Issue Resolved
        </button>
    </div>
</div>

<script>
function useTemplate(templateType) {
    const templates = {
        'thank_you': 'Thank you for contacting Champions Sports Bar & Grill. We appreciate your message and will get back to you soon.',
        'more_info': 'Thank you for your message. To better assist you, could you please provide more details about your inquiry?',
        'resolved': 'Thank you for bringing this to our attention. We have resolved the issue and appreciate your feedback.'
    };
    
    const template = templates[templateType];
    if (template) {
        const subject = document.getElementById('messageSubject').textContent;
        const email = document.getElementById('messageEmail').textContent;
        const mailtoLink = `mailto:${email}?subject=Re: ${encodeURIComponent(subject)}&body=${encodeURIComponent(template)}`;
        window.open(mailtoLink);
    }
}
</script>
