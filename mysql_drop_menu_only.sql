-- ============================================================================
-- Champions Sports Bar & Grill - MENU ONLY MySQL Drop Script
-- ============================================================================
-- 
-- ⚠️  WARNING: THIS WILL DELETE ALL MENU DATA! ⚠️
-- 
-- This script will:
-- 1. Delete all menu items from menu_items table
-- 2. Delete all menu categories from menu_categories table
-- 3. Optionally drop the entire menu tables (commented out by default)
-- 
-- This preserves all other website data (users, events, gallery, etc.)
-- Only the food menu system will be affected.
-- 
-- Database: champions_admin
-- Tables affected: menu_items, menu_categories
-- 
-- Created: 2025-07-16
-- ============================================================================

-- Use the database
USE champions_admin;

-- ============================================================================
-- OPTION 1: DELETE MENU DATA ONLY (Recommended)
-- ============================================================================
-- This preserves the table structure but removes all menu data

-- Show current menu statistics before deletion
SELECT 'BEFORE DELETION - Menu Statistics:' AS Status;
SELECT 
    (SELECT COUNT(*) FROM menu_items) AS total_menu_items,
    (SELECT COUNT(*) FROM menu_categories) AS total_categories;

-- Show menu categories that will be deleted
SELECT 'Menu Categories to be deleted:' AS Status;
SELECT id, name, description, sort_order FROM menu_categories ORDER BY sort_order;

-- Show count of menu items per category
SELECT 'Menu Items per Category:' AS Status;
SELECT 
    mc.name AS category_name,
    COUNT(mi.id) AS item_count
FROM menu_categories mc
LEFT JOIN menu_items mi ON mc.id = mi.category_id
GROUP BY mc.id, mc.name
ORDER BY mc.sort_order;

-- ============================================================================
-- DELETE MENU DATA (in correct order due to foreign keys)
-- ============================================================================

-- Step 1: Delete all menu items first (child table)
DELETE FROM menu_items;
SELECT 'All menu items deleted' AS Status;

-- Step 2: Delete all menu categories (parent table)
DELETE FROM menu_categories;
SELECT 'All menu categories deleted' AS Status;

-- Reset auto-increment counters to start fresh
ALTER TABLE menu_items AUTO_INCREMENT = 1;
ALTER TABLE menu_categories AUTO_INCREMENT = 1;
SELECT 'Auto-increment counters reset' AS Status;

-- ============================================================================
-- VERIFICATION
-- ============================================================================

-- Verify deletion was successful
SELECT 'AFTER DELETION - Verification:' AS Status;
SELECT 
    (SELECT COUNT(*) FROM menu_items) AS remaining_menu_items,
    (SELECT COUNT(*) FROM menu_categories) AS remaining_categories;

-- ============================================================================
-- OPTION 2: DROP MENU TABLES COMPLETELY (Uncomment if needed)
-- ============================================================================
-- WARNING: This will remove the entire menu system structure
-- Uncomment the lines below ONLY if you want to completely remove menu functionality

/*
-- Drop menu_items table first (has foreign key to menu_categories)
DROP TABLE IF EXISTS menu_items;
SELECT 'menu_items table dropped' AS Status;

-- Drop menu_categories table
DROP TABLE IF EXISTS menu_categories;
SELECT 'menu_categories table dropped' AS Status;
*/

-- ============================================================================
-- WHAT WAS AFFECTED
-- ============================================================================

/*
DELETED DATA:
- All menu items (food, drinks, appetizers, etc.)
- All menu categories (Appetizers, Burgers, Sandwiches, Entrees, Salads, Drinks, etc.)
- All pricing information
- All menu descriptions and allergen information
- All menu item images and featured status

PRESERVED DATA:
- Admin users and login accounts
- Site settings and configuration
- Content sections and hero banners
- Events and happenings
- Gallery images
- Job postings and applications
- Contact messages
- Admin activity logs

TABLE STRUCTURE:
- menu_items table structure preserved (if using Option 1)
- menu_categories table structure preserved (if using Option 1)
- All foreign key relationships maintained
*/

-- ============================================================================
-- RECOVERY INSTRUCTIONS
-- ============================================================================

/*
TO RESTORE MENU DATA:

1. Re-import from JSON file:
   php admin/import_menu_items.php admin/menu_items.json

2. Manually recreate through admin panel:
   - Go to: http://localhost:8000/admin/menu-categories.php
   - Add categories first
   - Go to: http://localhost:8000/admin/menu-items.php
   - Add individual menu items

3. Restore from database backup (if available):
   mysql -u champions_user -p champions_admin < backup_file.sql

4. Re-run default menu setup:
   - The default categories will be recreated if you run admin/database.sql
   - Default categories: Appetizers, Burgers, Sandwiches, Entrees, Salads, Drinks
*/

-- ============================================================================
-- ADMIN PANEL IMPACT
-- ============================================================================

/*
ADMIN PANEL PAGES AFFECTED:
- Menu Categories page will show empty list
- Menu Items page will show empty list
- Website menu page will show "No menu items available"

ADMIN PANEL PAGES NOT AFFECTED:
- Dashboard
- Content management
- Events management
- Gallery management
- Job postings
- Contact messages
- Settings
- User management
*/

SELECT 'Champions Sports Bar menu deletion completed!' AS Status;
SELECT 'Menu tables are now empty but structure preserved.' AS Note;
SELECT 'Use recovery instructions above to restore menu data.' AS Recovery;
