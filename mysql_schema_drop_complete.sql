-- ============================================================================
-- Champions Sports Bar & Grill - COMPLETE MySQL Schema Drop Script
-- ============================================================================
-- 
-- ⚠️  WARNING: THIS SCRIPT WILL COMPLETELY DESTROY ALL DATA! ⚠️
-- 
-- This script will:
-- 1. Drop all tables in the correct order (respecting foreign key constraints)
-- 2. Drop the entire database
-- 3. Drop the database user
-- 
-- USE WITH EXTREME CAUTION - ALL DATA WILL BE PERMANENTLY LOST!
-- 
-- Database: champions_admin
-- User: champions_user
-- 
-- Created: 2025-07-16
-- ============================================================================

-- Set SQL mode to handle foreign key constraints properly
SET FOREIGN_KEY_CHECKS = 0;
SET SQL_MODE = 'NO_AUTO_VALUE_ON_ZERO';

-- ============================================================================
-- STEP 1: DROP ALL TABLES (in dependency order)
-- ============================================================================

-- Use the database first
USE champions_admin;

-- Drop tables with foreign key dependencies first
DROP TABLE IF EXISTS admin_activity_log;
DROP TABLE IF EXISTS contact_messages;
DROP TABLE IF EXISTS gallery_images;
DROP TABLE IF EXISTS job_applications;
DROP TABLE IF EXISTS job_postings;
DROP TABLE IF EXISTS events;
DROP TABLE IF EXISTS menu_items;
DROP TABLE IF EXISTS hero_banners;
DROP TABLE IF EXISTS content_sections;
DROP TABLE IF EXISTS content_pages;  -- Referenced in test files
DROP TABLE IF EXISTS site_settings;

-- Drop parent tables last
DROP TABLE IF EXISTS menu_categories;
DROP TABLE IF EXISTS admin_users;

-- ============================================================================
-- STEP 2: DROP DATABASE
-- ============================================================================

-- Drop the entire database
DROP DATABASE IF EXISTS champions_admin;

-- ============================================================================
-- STEP 3: DROP DATABASE USER
-- ============================================================================

-- Drop the database user and revoke all privileges
DROP USER IF EXISTS 'champions_user'@'localhost';
FLUSH PRIVILEGES;

-- ============================================================================
-- VERIFICATION QUERIES (Optional - Run these to verify cleanup)
-- ============================================================================

-- Uncomment these lines to verify the cleanup was successful:

-- Check if database exists (should return empty result)
-- SHOW DATABASES LIKE 'champions_admin';

-- Check if user exists (should return empty result)
-- SELECT User FROM mysql.user WHERE User = 'champions_user';

-- ============================================================================
-- COMPLETE TABLE LIST THAT WAS DROPPED
-- ============================================================================

/*
CORE TABLES:
- admin_users              (Admin user accounts and permissions)
- site_settings           (Website configuration settings)
- content_sections        (Homepage content management)
- content_pages          (Page content management - from tests)
- hero_banners           (Hero banner/slider management)

CONTENT TABLES:
- menu_categories        (Menu category organization)
- menu_items            (Individual menu items)
- events                (Events and happenings)
- gallery_images        (Photo gallery management)

BUSINESS TABLES:
- job_postings          (Career job listings)
- job_applications      (Job application submissions)
- contact_messages      (Contact form messages)

SYSTEM TABLES:
- admin_activity_log    (Admin action logging)

DATABASE:
- champions_admin       (Main database)

USER:
- champions_user@localhost (Database user)
*/

-- ============================================================================
-- NOTES
-- ============================================================================

/*
1. This script drops ALL data permanently - there is no recovery
2. Foreign key constraints are temporarily disabled during the drop process
3. Tables are dropped in dependency order to avoid constraint violations
4. The database user is completely removed from MySQL
5. All privileges are flushed to ensure clean removal

WHAT THIS AFFECTS:
- All admin panel functionality will be broken
- All website content will be lost
- All user accounts and settings will be deleted
- All menu items, events, and gallery images will be removed
- All job applications and contact messages will be deleted
- All activity logs will be erased

TO RECREATE THE DATABASE:
1. Run the setup script: admin/setup-database.php
2. Or manually run: admin/database.sql
3. Or use the automated setup: setup-mysql.sh
*/

-- Re-enable foreign key checks
SET FOREIGN_KEY_CHECKS = 1;

-- ============================================================================
-- END OF SCRIPT
-- ============================================================================

SELECT 'Champions Sports Bar database schema drop completed!' AS Status;
