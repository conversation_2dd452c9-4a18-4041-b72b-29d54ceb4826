/* Champions Sports Bar & Grill - Custom Styles */

/* CSS Variables for consistent theming */
:root {
    --primary-color: #dc3545;
    --secondary-color: #28a745;
    --dark-color: #212529;
    --light-color: #f8f9fa;
    --accent-color: #ffc107;
    --text-dark: #333333;
    --text-light: #666666;
    --border-color: #dee2e6;
    --shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
    --transition: all 0.3s ease;
    --border-radius: 0.5rem;
    --font-family-primary: 'Roboto', sans-serif;
    --font-family-heading: '<PERSON>', sans-serif;
}

/* Global Styles */
* {
    box-sizing: border-box;
}

body {
    font-family: var(--font-family-primary);
    line-height: 1.6;
    color: var(--text-dark);
    padding-top: 76px; /* Account for fixed navbar */
}

h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-family-heading);
    font-weight: 600;
    line-height: 1.2;
}

.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: var(--transition);
    padding: 0.75rem 1.5rem;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: #c82333;
    border-color: #bd2130;
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

/* Navigation Styles */
.navbar {
    background-color: rgba(33, 37, 41, 0.95) !important;
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.navbar-brand {
    font-family: var(--font-family-heading);
    font-size: 1.5rem;
    font-weight: 700;
}

.navbar-nav .nav-link {
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    padding: 0.75rem 1rem !important;
    transition: var(--transition);
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
    color: var(--primary-color) !important;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)),
                url('../images/hero-bg.jpg') center/cover no-repeat;
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
    display: flex;
    align-items: center;
}

.hero-buttons .btn {
    margin: 0.5rem;
}

/* Patio Section */
.patio-section {
    padding: 5rem 0;
}

.patio-content h2,
.patio-content h3 {
    color: var(--secondary-color);
}

.patio-image img {
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.patio-image img:hover {
    transform: scale(1.05);
    box-shadow: var(--shadow-lg);
}

/* Gallery Preview */
.gallery-preview {
    background-color: var(--light-color);
}

.gallery-item {
    position: relative;
    overflow: hidden;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
}

.gallery-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.gallery-item img {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: var(--transition);
}

.gallery-item:hover img {
    transform: scale(1.1);
}

.gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition);
}

.gallery-item:hover .gallery-overlay {
    opacity: 1;
}

.gallery-overlay i {
    color: white;
    font-size: 2rem;
}

/* Features Section */
.features-section {
    padding: 5rem 0;
}

.feature-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    transition: var(--transition);
    border: 1px solid var(--border-color);
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-lg);
}

.feature-icon {
    color: var(--primary-color);
}

/* CTA Section */
.cta-section {
    background: linear-gradient(135deg, var(--primary-color), #c82333);
}

/* Menu Styles */
.menu-category {
    margin-bottom: 3rem;
}

.menu-item {
    border-bottom: 1px solid var(--border-color);
    padding: 1.5rem 0;
}

.menu-item:last-child {
    border-bottom: none;
}

.menu-item-header {
    display: flex;
    justify-content: between;
    align-items: flex-start;
    margin-bottom: 0.5rem;
}

.menu-item-name {
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 0;
}

.menu-item-price {
    font-weight: 700;
    color: var(--primary-color);
    font-size: 1.1rem;
}

.menu-item-description {
    color: var(--text-light);
    font-style: italic;
}

/* Gallery Grid */
.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    padding: 2rem 0;
}

/* Contact Form */
.contact-form {
    background: white;
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

.form-control {
    border-radius: var(--border-radius);
    border: 2px solid var(--border-color);
    padding: 0.75rem 1rem;
    transition: var(--transition);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

/* Map Container */
.map-container {
    height: 400px;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow);
}

/* Back to Top Button */
#backToTop {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
}

#backToTop:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
}

/* Responsive Design */
@media (max-width: 1199px) {
    .hero-section h1 {
        font-size: 3rem;
    }

    .gallery-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
}

@media (max-width: 991px) {
    body {
        padding-top: 70px; /* Adjust for smaller navbar */
    }

    .hero-section h1 {
        font-size: 2.8rem;
    }

    .feature-card {
        margin-bottom: 2rem;
    }

    .patio-section .row {
        flex-direction: column-reverse;
    }

    .patio-content {
        text-align: center;
        margin-top: 2rem;
    }
}

@media (max-width: 768px) {
    .hero-section {
        min-height: 80vh;
        padding: 2rem 0;
    }

    .hero-section h1 {
        font-size: 2.5rem;
        line-height: 1.2;
    }

    .hero-buttons .btn {
        display: block;
        width: 100%;
        margin: 0.5rem 0;
    }

    .menu-item-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .menu-item-price {
        margin-top: 0.5rem;
        font-size: 1.2rem;
    }

    .gallery-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .contact-card {
        margin-bottom: 1.5rem;
    }

    .hours-card .hour-item {
        font-size: 0.9rem;
    }

    .navbar-nav .nav-link {
        padding: 0.5rem 1rem !important;
        text-align: center;
    }

    .page-header {
        padding: 4rem 0 3rem;
    }

    .page-header h1 {
        font-size: 2.5rem;
    }
}

@media (max-width: 576px) {
    .hero-section h1 {
        font-size: 2rem;
    }

    .hero-section .lead {
        font-size: 1rem;
    }

    .display-5 {
        font-size: 2rem;
    }

    .display-6 {
        font-size: 1.5rem;
    }

    .menu-nav .btn {
        font-size: 0.8rem;
        padding: 0.5rem 0.75rem;
        margin: 0.25rem;
    }

    .gallery-item img {
        height: 200px;
    }

    .contact-form {
        padding: 1.5rem;
    }

    .footer .row > div {
        margin-bottom: 2rem;
    }

    .social-links a {
        margin: 0.5rem;
    }
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(220, 53, 69, 0.3);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Page Header Styles */
.page-header {
    padding: 6rem 0 4rem;
    background: linear-gradient(135deg, var(--dark-color), #495057);
}

.page-header h1 {
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

/* Contact Cards */
.contact-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    transition: var(--transition);
    height: 100%;
}

.contact-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.contact-icon {
    color: var(--primary-color);
}

/* Hours Card */
.hours-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

.hour-item {
    border-color: var(--border-color) !important;
}

/* Newsletter Form */
.newsletter-form .form-control {
    border-radius: var(--border-radius);
    border: 2px solid rgba(255, 255, 255, 0.3);
    background: rgba(255, 255, 255, 0.1);
    color: white;
}

.newsletter-form .form-control::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.newsletter-form .form-control:focus {
    border-color: white;
    background: rgba(255, 255, 255, 0.2);
    box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.animate-on-scroll.animated {
    opacity: 1;
    transform: translateY(0);
}

/* Utility Classes */
.text-primary {
    color: var(--primary-color) !important;
}

.bg-primary {
    background-color: var(--primary-color) !important;
}

.border-primary {
    border-color: var(--primary-color) !important;
}

.shadow-custom {
    box-shadow: var(--shadow-lg) !important;
}

.rounded-custom {
    border-radius: var(--border-radius) !important;
}

/* Enhanced Menu Page Styles */
.menu-nav {
    z-index: 1020;
    backdrop-filter: blur(10px);
}

.menu-filter-btn {
    margin: 0.25rem;
    border-radius: 25px;
    padding: 0.5rem 1.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
    border: 2px solid var(--primary-color);
}

.menu-filter-btn.active {
    background: linear-gradient(135deg, var(--primary-color), #c82333);
    border-color: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
}

.menu-filter-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    background-color: var(--primary-color);
    color: white;
}

.menu-item .card {
    transition: all 0.3s ease;
    border: none;
    overflow: hidden;
    height: 100%;
}

.menu-item .card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.menu-item-image {
    position: relative;
    overflow: hidden;
    height: 200px;
}

.menu-item-image img {
    transition: transform 0.3s ease;
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.menu-item .card:hover .menu-item-image img {
    transform: scale(1.05);
}

/* Menu Image Overlay */
.menu-image-link {
    position: relative;
    display: block;
    text-decoration: none;
}

.menu-image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(220, 53, 69, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 5;
}

.menu-image-overlay i {
    color: white;
    font-size: 2rem;
    transform: scale(0.8);
    transition: transform 0.3s ease;
}

.menu-image-link:hover .menu-image-overlay {
    opacity: 1;
}

.menu-image-link:hover .menu-image-overlay i {
    transform: scale(1);
}

/* Menu items without images - no special styling needed */

/* Thumbnail specific styles */
.menu-thumbnail {
    border-radius: 0.375rem 0.375rem 0 0;
}

/* Fancybox customization */
.fancybox__container {
    --fancybox-bg: rgba(24, 24, 27, 0.92);
}

.fancybox__toolbar {
    display: none !important;
}

.fancybox__nav {
    display: none !important;
}

.fancybox__counter {
    display: none !important;
}

.fancybox__caption {
    background: linear-gradient(135deg, var(--primary-color), #c82333);
    color: white;
    font-weight: 600;
    text-align: center;
    padding: 1rem;
    border-radius: 0.5rem;
    margin: 1rem;
}

.price {
    color: var(--primary-color) !important;
    font-weight: 700;
    font-size: 1.25rem;
}

.menu-category h2 {
    position: relative;
    margin-bottom: 2rem;
    color: var(--primary-color);
}

.menu-category h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 60px;
    height: 3px;
    background: linear-gradient(135deg, var(--primary-color), #c82333);
    border-radius: 2px;
}

.menu-search .input-group {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border-radius: 25px;
    overflow: hidden;
    max-width: 500px;
    margin: 0 auto;
}

.menu-search .form-control {
    border: none;
    padding: 1rem 1.5rem;
    font-size: 1.1rem;
    background: white;
}

.menu-search .form-control:focus {
    box-shadow: none;
    border-color: var(--primary-color);
}

.menu-search .btn {
    border: none;
    padding: 1rem 1.5rem;
    background: linear-gradient(135deg, var(--primary-color), #c82333);
    color: white;
}

.menu-search .btn:hover {
    background: linear-gradient(135deg, #c82333, var(--primary-color));
}

.menu-cta {
    background: linear-gradient(135deg, var(--primary-color), #c82333);
}

.badge.bg-warning {
    background: linear-gradient(135deg, #ffc107, #ffb300) !important;
    color: #000 !important;
    font-weight: 600;
}

.badge.bg-light {
    background: #f8f9fa !important;
    color: #495057 !important;
    border: 1px solid #dee2e6;
}

.badge.bg-danger {
    background: linear-gradient(135deg, var(--primary-color), #c82333) !important;
}

/* Menu item details */
.menu-item-details .badge {
    font-size: 0.75rem;
    padding: 0.35rem 0.65rem;
    margin-right: 0.5rem;
    margin-bottom: 0.25rem;
    border-radius: 15px;
}

.menu-item-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.menu-item .card-title {
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 0;
    font-size: 1.1rem;
}

.menu-item .card-text {
    color: var(--text-light);
    line-height: 1.5;
    margin-bottom: 1rem;
}

/* Featured item styling */
.menu-item .badge.bg-warning {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* Responsive menu adjustments */
@media (max-width: 768px) {
    .menu-filter-btn {
        font-size: 0.875rem;
        padding: 0.375rem 1rem;
        margin: 0.125rem;
    }

    .menu-category h2 {
        font-size: 1.75rem;
        text-align: center;
    }

    .price {
        font-size: 1.1rem;
    }

    .menu-item .card-body {
        padding: 1rem;
    }

    .menu-item-header {
        flex-direction: column;
        align-items: flex-start;
        text-align: left;
    }

    .menu-item-header .price {
        margin-top: 0.5rem;
        align-self: flex-end;
    }

    .menu-search .input-group {
        margin: 0;
    }

    .menu-nav .d-flex {
        justify-content: center;
        flex-wrap: wrap;
    }

    /* Mobile image adjustments */
    .menu-item-image {
        height: 180px;
    }

    .menu-image-overlay i {
        font-size: 1.5rem;
    }

    /* Fancybox mobile adjustments */
    .fancybox__caption {
        font-size: 0.9rem;
        padding: 0.75rem;
        margin: 0.5rem;
    }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
    .menu-image-overlay {
        opacity: 0.7;
    }

    .menu-item .card:hover .menu-item-image img {
        transform: none;
    }

    .menu-image-link:hover .menu-image-overlay {
        opacity: 0.9;
    }
}
